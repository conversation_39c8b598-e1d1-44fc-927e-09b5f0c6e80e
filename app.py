import logging

from fastapi import FastAPI
from starlette.requests import Request
from starlette.responses import JSONResponse

import companyCrawler.entrypoints


logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("main")

app = FastAPI()
app.include_router(companyCrawler.entrypoints.router)

@app.get("/")
async def root():
    return {"message": "Hello World"}

@app.exception_handler(Exception)
async def unhandled_exception_handler(request: Request, exc: Exception):
    logger.exception(
        f"Unhandled exception occurred during request: {request.url}"
    )
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"},
    )
