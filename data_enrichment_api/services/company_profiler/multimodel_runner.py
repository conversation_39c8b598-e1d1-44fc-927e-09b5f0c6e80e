# data_enrichment_api/services/company_profiler/multimodel_runner.py
"""
Multi-model runner for company profiling.
This module provides functionality to run multiple LLM models in parallel to analyze company websites
and generate structured profiles. It handles chunking, model execution, and result merging.
"""

import logging, tiktoken, re, json
from typing import List

# from data_enrichment_api.llm_clients.gpt_o3_client import GPTO3<PERSON>lient
# from data_enrichment_api.llm_clients.gpt_o4_mini_client import GPTO4MiniClient
from data_enrichment_api.services.company_profiler.prompts import system_prompt
from data_enrichment_api.llm_clients.gpt4o_mini_client import GPT4<PERSON><PERSON><PERSON>lient
# from data_enrichment_api.llm_clients.gpt41_nano_client import GPT41<PERSON><PERSON><PERSON>lient
# from data_enrichment_api.llm_clients.gpt41_flagship_client import GPT41<PERSON><PERSON>ship<PERSON>lient
# from data_enrichment_api.llm_clients.gpt41_mini_client import GPT41<PERSON>ini<PERSON>lient
# from data_enrichment_api.llm_clients.claude35_sonnet_client import Claude<PERSON><PERSON>onnet<PERSON>lient
# from data_enrichment_api.llm_clients.claude37_sonnet_client import Claude37Sonnet<PERSON><PERSON>
# from data_enrichment_api.llm_clients.claude3_haiku_client import Claude3HaikuClient
# from llm_clients.grok3_mini_client import Grok3MiniClient

# Configure logger
logger = logging.getLogger('multimodel_runner')

class MultiModelRunner:
    """
    Runner for executing multiple LLM models on company website content.
    
    This class manages the execution of different LLM models to analyze company websites.
    It handles content chunking, parallel model execution, and merging of results.
    """
    def __init__(self):
        """
        Initialize the multi-model runner with available models and their configurations.
        
        Sets up:
        - Available LLM models and their pricing
        - Tokenizer for content chunking
        - Chunking configuration parameters
        """
        self.models = {
            "gpt-4o-mini": {
                "client": GPT4OMiniClient(),
                "pricing": {"input_token_usd": 0.00000015, "output_token_usd": 0.00000060}
            }#,
            # "gpt-4.1-nano": {
            #     "client": GPT41NanoClient(),
            #     "pricing": {"input_token_usd": 0.00000010, "output_token_usd": 0.00000040}
            # },
            # "gpt-4.1": {
            #     "client": GPT41FlagshipClient(),
            #     "pricing": {"input_token_usd": 0.00000200, "output_token_usd": 0.00000800}
            # },
            # "gpt-4.1-mini": {
            #     "client": GPT41MiniClient(),
            #     "pricing": {"input_token_usd": 0.00000040, "output_token_usd": 0.00000160}
            # },
            # "gpt-o3": {
            #     "client": GPTO3Client(),
            #     "pricing": {"input_token_usd": 0.00001, "output_token_usd": 0.00004}
            # },
            # "gpt-o4-mini": {
            #     "client": GPTO4MiniClient(),
            #     "pricing": {"input_token_usd": 0.0000011, "output_token_usd": 0.0000044}
            # },
            # "claude-3-7-sonnet-20250219": {
            #     "client": Claude37SonnetClient(),
            #     "pricing": {"input_token_usd": 0.00000300, "output_token_usd": 0.00001500}
            # },
            # "claude-3-5-sonnet-20240620": {
            #     "client": Claude35SonnetClient(),
            #     "pricing": {"input_token_usd": 0.00000300, "output_token_usd": 0.00001500}
            # },
            # "claude-3-haiku-20240307": {
            #     "client": Claude3HaikuClient(),
            #     "pricing": {"input_token_usd": 0.00000025, "output_token_usd": 0.00000125}
            # }   
        }

        # Shared tokenizer and chunking config
        self.chunking_encoding = tiktoken.encoding_for_model("gpt-4o-mini")
        self.max_chunk_size = 10000
        self.overlap_tokens = 200

    def chunk_content_once(self, content: str) -> List[str]:
        """
        Split content into chunks based on page boundaries and token limits.
        
        Args:
            content (str): The content to split into chunks
            
        Returns:
            List[str]: List of content chunks, each with its page title/URL
        """
        # Split content into pages using the separator
        pages = content.split('---')
        
        # Filter out empty pages and clean them
        chunks = []
        for page in pages:
            page = page.strip()
            if not page:
                continue
                
            # Get the page title/URL from the first line
            lines = page.split('\n')
            if len(lines) > 0 and lines[0].startswith('# Page:'):
                page_url = lines[0]
            else:
                page_url = "Unknown Page"
                
            # Clean the page content
            page_content = '\n'.join(lines[1:]).strip()
            
            # Only include non-empty pages
            if page_content:
                # Check if page needs to be split by tokens
                page_tokens = len(self.chunking_encoding.encode(page_content))
                if page_tokens > self.max_chunk_size:
                    # Split page into smaller chunks
                    sub_chunks = self.chunk_content_by_tokens(page_content)
                    for i, sub_chunk in enumerate(sub_chunks):
                        chunks.append(f"{page_url} (Part {i+1}/{len(sub_chunks)})\n\n{sub_chunk}")
                else:
                    chunks.append(f"{page_url}\n\n{page_content}")
        
        logger.info(f"Split content into {len(chunks)} chunks")
        return chunks

    def chunk_content_by_tokens(self, content: str) -> List[str]:
        """
        Split content into chunks based on token size.
        
        Args:
            content (str): The content to split into chunks
            
        Returns:
            List[str]: List of content chunks with token-based boundaries
        """
        encoding = self.chunking_encoding
        paragraphs = re.split(r'\n(?=\s*#)', content.strip())
        chunks = []
        current_chunk = []
        current_tokens = 0

        for para in paragraphs:
            para = para.strip()
            if not para:
                continue
            tokenized_para = encoding.encode(para)
            para_tokens = len(tokenized_para)

            if current_tokens + para_tokens > self.max_chunk_size:
                if current_chunk:
                    chunk_text = '\n\n'.join(current_chunk)
                    chunks.append(chunk_text)
                    tail_tokens = encoding.encode(chunk_text)[-self.overlap_tokens:]
                    tail_text = encoding.decode(tail_tokens)
                    current_chunk = [tail_text]
                    current_tokens = len(encoding.encode(tail_text))
            else:
                current_chunk.append(para)
                current_tokens += para_tokens

        if current_chunk:
            chunks.append('\n\n'.join(current_chunk))

        return chunks

    def extract_json_block(self, text: str) -> str:
        """
        Extract JSON block from LLM response text.
        
        Args:
            text (str): Text containing JSON block
            
        Returns:
            str: Extracted JSON string
        """
        try:
            text = text.strip()
            text = re.sub(r'^```json|```$', '', text.strip(), flags=re.MULTILINE).strip()
            match = re.search(r'\{[\s\S]+\}', text)
            if match:
                return match.group(0)
            return text
        except Exception as e:
            logger.warning(f"JSON extraction failed: {e}")
            return "{}"

    def default_profile(self) -> dict:
        """
        Get the default empty company profile structure.
        
        Returns:
            dict: Default profile structure with all fields set to None
        """
        return {
            "description": None,
            "found_company_name": None,
            "linkedin": None,
            "products_services": None,
            "products_services_categories": None,
            "customers_industries": None,
            "short_description": None,
            "categories": None,
            "industry": None,
            "subsectors": None,
            "business_model": None,
            "geographic_markets": None,
            "key_managers": [],
            "contact_details": {
                "phone": None,
                "email": None
            },
            "headquarters": {
                "street_address": None,
                "city": None,
                "zipcode": None,
                "country": None
            },
            "register_id": None,
            "register_court": None,
            "vat_id": None
        }

    def merge_json_chunks(self, chunk_data: List[dict]) -> dict:
        """
        Merge JSON data from multiple chunks into a single profile.
        
        Args:
            chunk_data (List[dict]): List of JSON data from chunks
            
        Returns:
            dict: Merged company profile
        """
        merged = self.default_profile()
        for entry in chunk_data:
            for key, val in entry.items():
                if val is None:
                    continue
                if isinstance(merged.get(key), list):
                    merged[key].extend(val)
                    merged[key] = [dict(t) for t in {tuple(d.items()) for d in merged[key]}]
                elif isinstance(merged.get(key), dict):
                    for subkey, subval in val.items():
                        if subval:
                            merged[key][subkey] = subval
                else:
                    if isinstance(val, str) and len(val) > len(merged.get(key, "") or ""):
                        merged[key] = val
        return merged

    async def run_all_models(self, cleaned_content: str, company_name: str) -> dict:
        """
        Run all models on the cleaned content.
        
        Args:
            cleaned_content (str): Cleaned website content to analyze
            company_name (str): Name of the company being analyzed
            
        Returns:
            dict: Dictionary containing profiles from all models with token usage and costs
        """
        results = {}
        chunks = self.chunk_content_once(cleaned_content)
        
        if not chunks:
            logger.error("No valid chunks found in content")
            return {
                model_name: {
                    "profile": self.default_profile(),
                    "error": "No valid content chunks found",
                    "tokens": {"input": 0, "output": 0, "total": 0},
                    "cost_usd": 0.0
                }
                for model_name in self.models.keys()
            }

        # Run all available models
        for model_name, cfg in self.models.items():
            try:
                logger.info(f"Running model: {model_name}")
                client = cfg["client"]
                pricing = cfg["pricing"]

                combined_data = []
                total_input_tokens = 0
                total_output_tokens = 0

                for i, chunk in enumerate(chunks):
                    logger.info(f"[{model_name}] Processing chunk {i + 1}/{len(chunks)}")

                    # Skip empty chunks
                    if not chunk.strip():
                        logger.warning(f"[{model_name}] Skipping empty chunk {i + 1}")
                        continue

                    try:
                        result = await client.run(
                            prompt=f"<website_extraction>\n{chunk}\n</website_extraction>",
                            system_prompt=system_prompt,
                            max_tokens=1800
                        )

                        usage = result.get("usage", {})
                        total_input_tokens += usage.get("prompt_tokens", 0)
                        total_output_tokens += usage.get("completion_tokens", 0)

                        raw_content = result["content"]
                        json_block = self.extract_json_block(raw_content)

                        try:
                            parsed = json.loads(json_block)
                            combined_data.append(parsed)
                        except json.JSONDecodeError as e:
                            logger.warning(f"[{model_name}] JSON parse failed for chunk {i + 1}: {e}")
                            logger.debug(f"Raw content: {raw_content}")
                    except Exception as e:
                        logger.error(f"[{model_name}] Error processing chunk {i + 1}: {e}")
                        continue

                if not combined_data:
                    logger.error(f"[{model_name}] No valid data extracted from any chunks")
                    results[model_name] = {
                        "profile": self.default_profile(),
                        "error": "No valid data extracted from content",
                        "tokens": {"input": 0, "output": 0, "total": 0},
                        "cost_usd": 0.0
                    }
                    continue

                cost = (
                    total_input_tokens * pricing["input_token_usd"] +
                    total_output_tokens * pricing["output_token_usd"]
                )

                results[model_name] = {
                    "profile": self.merge_json_chunks(combined_data),
                    "tokens": {
                        "input": total_input_tokens,
                        "output": total_output_tokens,
                        "total": total_input_tokens + total_output_tokens
                    },
                    "cost_usd": round(cost, 4)
                }

            except Exception as e:
                logger.error(f"Error running {model_name}: {e}")
                results[model_name] = {
                    "profile": self.default_profile(),
                    "error": str(e),
                    "tokens": {"input": 0, "output": 0, "total": 0},
                    "cost_usd": 0.0
                }

        return results

    async def run_all_models_for_chunks_list(self, chunks: List[str]) -> dict:
        """
        Run all models on the cleaned content.

        Args:
            # cleaned_content (str): Cleaned website content to analyze
            chunks (List[str]): Cleaned website content to analyze passed in chunks

        Returns:
            dict: Dictionary containing profiles from all models with token usage and costs
        """
        results = {}

        # Run all available models
        for model_name, cfg in self.models.items():
            try:
                logger.info(f"Running model: {model_name}")
                client = cfg["client"]
                pricing = cfg["pricing"]

                combined_data = []
                total_input_tokens = 0
                total_output_tokens = 0

                # logger.info(f"[{model_name}] Processing {len(cleaned_content)} chars")
                # try:
                #     result = await client.run(
                #         prompt=f"<website_extraction>\n{cleaned_content}\n</website_extraction>",
                #         system_prompt=system_prompt,
                #         # max_tokens=1800
                #         max_tokens=8000
                #     )
                #
                #     usage = result.get("usage", {})
                #     total_input_tokens += usage.get("prompt_tokens", 0)
                #     total_output_tokens += usage.get("completion_tokens", 0)
                #
                #     raw_content = result["content"]
                #     json_block = self.extract_json_block(raw_content)
                #
                #     try:
                #         parsed = json.loads(json_block)
                #         combined_data.append(parsed)
                #     except json.JSONDecodeError as e:
                #         logger.warning(f"[{model_name}] JSON parse failed: {e}")
                #         logger.debug(f"Raw content: {raw_content}")
                # except Exception as e:
                #     logger.error(f"[{model_name}] Error processing md: {e}")
                #     continue
                for i, chunk in enumerate(chunks):
                    logger.info(f"[{model_name}] Processing chunk {i + 1}/{len(chunks)}")

                    # Skip empty chunks
                    # if not chunk.strip():
                    if not chunk:
                        logger.warning(f"[{model_name}] Skipping empty chunk {i + 1}")
                        continue

                    try:
                        result = await client.run(
                            prompt=f"<website_extraction>\n{chunk}\n</website_extraction>",
                            system_prompt=system_prompt,
                            # max_tokens=1800
                            max_tokens = 8000
                        )

                        usage = result.get("usage", {})
                        total_input_tokens += usage.get("prompt_tokens", 0)
                        total_output_tokens += usage.get("completion_tokens", 0)

                        raw_content = result["content"]
                        json_block = self.extract_json_block(raw_content)

                        try:
                            parsed = json.loads(json_block)
                            combined_data.append(parsed)
                        except json.JSONDecodeError as e:
                            logger.warning(f"[{model_name}] JSON parse failed for chunk {i + 1}: {e}")
                            logger.debug(f"Raw content: {raw_content}")
                    except Exception as e:
                        logger.error(f"[{model_name}] Error processing chunk {i + 1}: {e}")
                        continue


                if not combined_data:
                    logger.error(f"[{model_name}] No valid data extracted from any chunks")
                    results[model_name] = {
                        "profile": self.default_profile(),
                        "error": "No valid data extracted from content",
                        "tokens": {"input": 0, "output": 0, "total": 0},
                        "cost_usd": 0.0
                    }
                    continue

                cost = (
                    total_input_tokens * pricing["input_token_usd"] +
                    total_output_tokens * pricing["output_token_usd"]
                )

                results[model_name] = {
                    "profile": self.merge_json_chunks(combined_data),
                    "tokens": {
                        "input": total_input_tokens,
                        "output": total_output_tokens,
                        "total": total_input_tokens + total_output_tokens
                    },
                    "cost_usd": round(cost, 4)
                }

            except Exception as e:
                logger.error(f"Error running {model_name}: {e}")
                results[model_name] = {
                    "profile": self.default_profile(),
                    "error": str(e),
                    "tokens": {"input": 0, "output": 0, "total": 0},
                    "cost_usd": 0.0
                }

        return results

