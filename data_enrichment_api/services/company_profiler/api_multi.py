# data_enrichment_api/services/company_profiler/api_multi.py
"""
FastAPI router for multi-model company profiling endpoints.
This module provides the API interface for running multiple LLM models to analyze company websites.
"""

from fastapi import APIRouter
from pydantic import BaseModel
from data_enrichment_api.services.company_profiler.multimodel_runner import MultiModelRunner

router = APIRouter()

class AnalyzeMultiRequest(BaseModel):
    """
    Request model for multi-model company analysis.
    
    Attributes:
        cleaned_pages (dict[str, str]): Dictionary of cleaned page content from the crawler
    """
    cleaned_pages: dict[str, str]

@router.post("/analyze")
async def analyze_company_multi(request: AnalyzeMultiRequest):
    """
    Analyze company website content using multiple LLM models.
    
    Args:
        request (AnalyzeMultiRequest): Request containing cleaned page content
        
    Returns:
        dict: Dictionary containing profiles generated by multiple models
    """
    runner = MultiModelRunner()
    combined_text = "\n\n".join(request.cleaned_pages.values())
    profiles = await runner.run_all_models(combined_text)

    return {"multi_model_profiles": profiles}
