# data_enrichment_api/services/validator/core.py
"""
URL validation service for company websites.
This module provides functions to validate company URLs and check their authenticity.
"""

import re, requests, urllib3, logging
from typing import Tuple, Dict
from urllib.parse import urlparse
from datetime import datetime

logger = logging.getLogger('validator')

# Disable SSL verification warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def is_valid_url(url: str) -> Tuple[bool, str]:
    try:
        # Ensure URL has a protocol
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url

        response = requests.get(url, timeout=5, allow_redirects=True, verify=False)

        # Check if URL was redirected
        final_url = response.url
        if final_url != url:
            return True, f"Redirected to: {final_url}"

        return True, ""
    except requests.RequestException as e:
        return False, f"Connection error: {str(e)}"

def extract_domain(url: str) -> str:
    """
    Extract the domain from a URL, removing 'www.' prefix if present.
    
    Args:
        url (str): The URL to extract domain from
        
    Returns:
        str: The extracted domain
    """
    logger.info(f"[DOMAIN EXTRACTION] Starting domain extraction for URL: {url}")

    # Ensure URL has a protocol
    if not url.startswith(('http://', 'https://')):
        logger.info(f"[DOMAIN EXTRACTION] Adding https:// protocol to URL: {url}")
        url = 'https://' + url
        
    # Parse URL and extract domain
    parsed_url = urlparse(url)
    domain = parsed_url.netloc
    
    # Remove 'www.' prefix if present
    if domain.startswith("www."):
        logger.info(f"[DOMAIN EXTRACTION] Removing 'www.' prefix from domain: {domain}")
        domain = domain[4:]

    logger.info(f"[DOMAIN EXTRACTION] Extracted domain: {domain}")
    return domain

def validate_company_url(company_name: str, url: str) -> Dict:
    domain = extract_domain(url)

    if re.search(r"fake", domain, re.IGNORECASE):
        return {
            "company_name": company_name,
            "url": url,  # Always return the original URL
            "is_valid_url": False,
            "matches_company": False,
            "domain": domain,
            "validation_notes": "Fake domain detected"
        }

    # Check URL accessibility
    try:
        is_valid, error = is_valid_url(url)
    except Exception as e:
        is_valid, error = False, str(e)

    if not is_valid:
        return {
            "company_name": company_name,
            "url": url,  # Always return the original URL
            "is_valid_url": False,
            "matches_company": False,
            "domain": domain,
            "validation_notes": f"URL validation failed: {error}"
        }

    return {
        "company_name": company_name,
        "url": url,
        "is_valid_url": True,
        "matches_company": True,
        "domain": domain,
        "validation_notes": "URL is valid and accessible"
    }