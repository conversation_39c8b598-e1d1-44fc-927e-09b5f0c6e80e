# data_enrichment_api/services/validator/api.py
"""
FastAPI router for URL validation endpoints.
This module provides the API interface for the URL validation service.
"""
from fastapi import APIRouter
from pydantic import BaseModel
from data_enrichment_api.services.validator.core import validate_company_url

router = APIRouter()

class ValidationRequest(BaseModel):
    """
    Request model for URL validation.
    
    Attributes:
        company_name (str): Name of the company
        url (str): Website URL to validate
    """
    company_name: str
    url: str

@router.post("/validate")
async def validate(request: ValidationRequest):
    """
    Validate a company's website URL.
    
    Args:
        request (ValidationRequest): Request containing company name and URL
        
    Returns:
        Dict: Validation results containing:
            - company_name: Original company name
            - url: Original or 'invalid' if validation failed
            - is_valid_url: Boolean indicating URL validity
            - matches_company: Boolean indicating if URL matches company
            - domain: Extracted domain
            - validation_notes: Detailed validation notes
    """
    result = validate_company_url(request.company_name, request.url)
    return result
