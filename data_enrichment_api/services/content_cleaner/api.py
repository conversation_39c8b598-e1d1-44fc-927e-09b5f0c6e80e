# data_enrichment_api/services/content_cleaner/api.py
"""
FastAPI router for content cleaning endpoints.
This module provides the API interface for the content cleaning service.
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict
from .core import clean_markdown_pages

router = APIRouter()

class MarkdownInput(BaseModel):
    """
    Request model for content cleaning.
    
    Attributes:
        pages (Dict[str, str]): Dictionary of pages to clean, where keys are page paths/URLs
                               and values are raw markdown content
    """
    pages: Dict[str, str]  # keys = page path or URL, values = raw markdown

@router.post("/clean")
async def clean_pages(input_data: MarkdownInput):
    """
    Clean multiple pages of markdown content.
    
    Args:
        input_data (MarkdownInput): Input containing pages to clean
        
    Returns:
        Dict[str, Dict[str, str]]: Dictionary containing cleaned pages
        
    Raises:
        HTTPException: If cleaning fails
    """
    try:
        cleaned = await clean_markdown_pages(input_data.pages)
        return {"cleaned": cleaned}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
