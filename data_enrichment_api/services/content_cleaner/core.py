# data_enrichment_api/services/content_cleaner/core.py
"""
Content cleaning service for markdown content.
This module provides functions to clean and process markdown content by removing unwanted elements
like cookie banners, tracking notices, and other noise while preserving the main content.
"""

import re, asyncio, logging, os
from openai import OpenAI
from typing import Dict, Tuple
from dotenv import load_dotenv
from datetime import datetime

# Configure logger
logger = logging.getLogger('content_cleaner')

load_dotenv()
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

# Patterns for simple content removal
SIMPLE_PATTERNS = [
    r"\[!\[.*?\]\(.*?\)\]",  # Nested image links
    r"!\[.*?\]\(.*?\)",      # Image links
    r"\[.*?\]\(.*?\)",       # Regular links
    r"\[.*?\]\[#.*?\]",      # Reference links
    r"\[#.*?#\]",            # Anchor links
    r"(?i)deny allow selection customize allow all",  # Cookie consent text
    r"(?i)benachrichtigungen",  # Notifications text
    r"\*\s*$",               # Empty bullet points
    r"\*\s+\*",              # Double bullet points
    r"(?i)^:?\s*(HTML|HTTP|IndexedDB|Pixel Tracker|Session|Persistent)?\s*$",  # Technical headers
    r"(?i)^pending\s*$"      # Pending status
]

def remove_cookie_sections(text: str) -> str:
    """
    Remove cookie-related sections from the text.
    
    Args:
        text (str): Input text containing cookie sections
        
    Returns:
        str: Text with cookie sections removed
    """
    logger.info("[COOKIE REMOVAL] Starting cookie section removal")
    original_length = len(text)

    COOKIE_SECTION_HEADERS = [
        r"(?i)^details\s*$",
        r"(?i)^necessary\s*$",
        r"(?i)^preferences\s*$",
        r"(?i)^statistics\s*$",
        r"(?i)^marketing\s*$",
        r"(?i)^unclassified\s*$"
    ]
    COOKIE_SECTION_END = re.compile(r"(?i)^\s*(impressum|kontakt|about|referenzen|\*\s*\S+|[#]{1,6}\s+.*?)")

    lines = text.splitlines()
    keep = []
    skip_mode = False
    buffer = []
    sections_removed = 0

    i = 0
    while i < len(lines):
        line = lines[i]
        if any(re.match(header, line.strip()) for header in COOKIE_SECTION_HEADERS):
            logger.info(f"[COOKIE REMOVAL] Found cookie section header: {line.strip()}")
            skip_mode = True
            buffer = [line]
            i += 1
            while i < len(lines):
                next_line = lines[i]
                if COOKIE_SECTION_END.match(next_line.strip()):
                    skip_mode = False
                    break
                buffer.append(next_line)
                i += 1
            section = "\n".join(buffer)
            if len(re.findall(r"(?i)cookie|personal data|consent|local storage", section)) >= 3:
                logger.info("[COOKIE REMOVAL] Removing cookie section based on keyword count")
                buffer = []
                sections_removed += 1
            keep.extend(buffer)
            continue
        keep.append(line)
        i += 1

    result = "\n".join(keep)
    logger.info(f"[COOKIE REMOVAL] Removed {sections_removed} cookie sections")
    logger.info(f"[COOKIE REMOVAL] Text length reduced from {original_length} to {len(result)} characters")
    return result

def llm_clean_content(text: str) -> str:
    """
    Clean content using LLM to remove unwanted elements while preserving structure.
    
    Args:
        text (str): Input text to clean
        
    Returns:
        str: Cleaned text with unwanted elements removed
    """
    logger.info("[LLM CLEAN] Starting LLM-based content cleaning")
    original_length = len(text)

    prompt = (
        "You are a content cleaning assistant.\n"
        "Your job is to clean up the input markdown by removing only cookie consent banners, tracking notices, privacy/legal boilerplate, etc.\n"
        "DO NOT invent or generate new content. DO NOT add headings or section titles unless they already exist in the original.\n"
        "Preserve the existing structure of the input as closely as possible, only cleaning out unwanted noise.\n"
        "Return only the cleaned markdown content, without any enhancements or summaries."
    )

    try:
        logger.info("[LLM CLEAN] Sending content to LLM for cleaning")
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": prompt},
                {"role": "user", "content": text[:15000]}
            ],
            temperature=0.0,
        )
        cleaned_text = response.choices[0].message.content.strip()
        logger.info(f"[LLM CLEAN] Content cleaned successfully. Length reduced from {original_length} to {len(cleaned_text)} characters")
        return cleaned_text
    except Exception as e:
        logger.error(f"[LLM CLEAN] Error during LLM cleaning: {str(e)}")
        return text

async def clean_page(filename: str, content: str) -> Tuple[str, str]:
    try:
        content = remove_cookie_sections(content)

        # Remove common unwanted patterns
        content = re.sub(r"(?i)(powered by|webdesign by|realisiert durch).+?(\n|$)", "", content)
        content = re.sub(r"(?i)(info@nextlevels\.com|next levels gmbh|webagentur|online-präsenz).+?(\n|$)", "", content)
        content = re.sub(r"(?i)^.*alle rechte vorbehalten.*$", "", content, flags=re.MULTILINE)
        content = re.sub(r"(?i)^.*datenschutz.*$", "", content, flags=re.MULTILINE)
        content = re.sub(r"(?i)^.*nutzungsbedingungen.*$", "", content, flags=re.MULTILINE)
        content = re.sub(r"(?i)^.*cookies.*$", "", content, flags=re.MULTILINE)

        # Remove simple patterns
        for pattern in SIMPLE_PATTERNS:
            content = re.sub(pattern, "", content, flags=re.MULTILINE | re.DOTALL)

        # Remove type-related content
        content = re.sub(r"\bType[A-Za-z0-9_-]+", "", content)
        content = re.sub(r"\bType\b", "", content)
        content = re.sub(r"(\n\s*){2,}", "\n\n", content)
        content = content.strip()
        if not content:
            return filename, ""

        # Use LLM for final cleaning
        cleaned = await asyncio.to_thread(llm_clean_content, content)
        return filename, cleaned
    except Exception as e:
        return filename, content

async def clean_markdown_pages(pages: Dict[str, str]) -> Dict[str, str]:
    tasks = [clean_page(filename, content) for filename, content in pages.items()]
    results = await asyncio.gather(*tasks)

    cleaned_pages = {filename: cleaned for filename, cleaned in results if cleaned}

    return cleaned_pages
