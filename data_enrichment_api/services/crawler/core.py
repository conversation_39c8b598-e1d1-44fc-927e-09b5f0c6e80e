# data_enrichment_api/services/crawler/core.py
"""
Website crawler service for company websites.
This module provides functions to crawl websites, extract content, and manage caching.
"""

import re, hashlib, os, logging, aiohttp
from typing import Dict, List, Set
from urllib.parse import urlparse, urljoin, parse_qs, unquote
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig, CacheMode, MemoryAdaptiveDispatcher
from dotenv import load_dotenv
# from crawl4ai.content_filter_strategy import PruningContentFilter
# from crawl4ai.markdown_generation_strategy import DefaultMarkdownGenerator
from data_enrichment_api.utils.file_store import save_page_content, save_cached_pages
from data_enrichment_api.config import settings

from crawl4ai.deep_crawling.scorers import URLScorer

# Load environment variables
load_dotenv()

class CompanyCrawler:
    """
    Company-specific crawler that manages its own cache and state.
    This ensures isolation between different companies being crawled in parallel.
    """
    def __init__(self, company_id: str, company_name: str):
        self.company_id = company_id
        self.company_name = company_name
        self.cached_pages = {}
        self.failed_urls = []
        self.existing_slugs = set()
        self.domain_crawl_delays = {}
        self.crawled_urls = set()

    def reset_cache(self):
        """Reset all cache and state for this company."""
        self.cached_pages.clear()
        self.failed_urls.clear()
        self.existing_slugs.clear()
        self.domain_crawl_delays.clear()
        self.crawled_urls.clear()

    def get_cached_markdown_dump(self) -> str:
        """Get all cached markdown content for this company as a single string."""
        merged = []
        for url, content in self.cached_pages.items():
            page_path = urlparse(url).path.strip("/") or "index"
            if isinstance(content, str):
                markdown = content
            else:
                markdown = content.get("markdown", "")
            if markdown:
                merged.append(f"# {page_path}\n\n{markdown.strip()}\n")
        return "\n\n".join(merged)

    def make_slug(self, url: str) -> str:
        """Create a URL slug for caching, specific to this company."""
        parsed = urlparse(url)
        path = parsed.path
        query = parsed.query

        # Try to use ObjectPath if present in query
        qs = parse_qs(query)
        if "ObjectPath" in qs and qs["ObjectPath"]:
            object_path = unquote(qs["ObjectPath"][0])
            slug_parts = [p for p in object_path.strip("/").split("/") if p]
        else:
            slug_parts = [p for p in path.strip("/").split("/") if p]
        if not slug_parts:
            return "index"

        # Create a unique slug for this company
        slug = "_".join(slug_parts)
        if slug in self.existing_slugs:
            # Add a hash suffix if slug already exists
            url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
            slug = f"{slug}_{url_hash}"

        self.existing_slugs.add(slug)
        return slug

# Initialize crawler state
cached_pages = {}
failed_urls = []
existing_slugs = set()
domain_crawl_delays = {}

# Crawler Settings
MAX_DEPTH = int(os.getenv("MAX_DEPTH", 1))
PAGE_TIMEOUT_MS = int(os.getenv("PAGE_TIMEOUT_MS", 30000))
CRAWL_DELAY_SECONDS = float(os.getenv("CRAWL_DELAY_SECONDS", 0.3))
MAX_INTERNAL_LINKS = int(os.getenv("MAX_INTERNAL_LINKS", 20))

# Priority patterns for important pages (in order of importance)
PRIORITY_PATTERNS = [
    # Company Information
    (r'/about', 10),           # About page
    (r'/company', 10),         # Company page
    (r'/who-we-are', 10),      # Who we are page
    (r'/our-story', 10),       # Company story/history

    # Products & Services
    (r'/products', 9),         # Products page
    (r'/services', 9),         # Services page
    (r'/solutions', 9),        # Solutions page
    (r'/offerings', 9),        # Offerings page

    # Team & Leadership
    (r'/team', 8),             # Team page
    (r'/leadership', 8),       # Leadership page
    (r'/management', 8),       # Management page
    (r'/people', 8),           # People page

    # Contact & Support
    (r'/contact', 7),          # Contact page
    (r'/support', 7),          # Support page
    (r'/help', 7),             # Help page

    # Other Important Pages
    (r'/careers', 6),          # Careers page
    (r'/news', 6),             # News page
    (r'/blog', 6),             # Blog page
    (r'/press', 6),            # Press page
    (r'/media', 6),            # Media page
]

# GCS Configuration
GCS_BUCKET_NAME = os.getenv("GCS_BUCKET_NAME")
PROJECT_ID = os.getenv("PROJECT_ID")

# URL patterns to exclude from crawling
BLACKLIST_PATTERNS = [
    r"^/datenschutz/?$",  # Privacy policy
    r"^/privacy/?$",      # Privacy policy
    r"^/agb/?$",          # Terms and conditions
    r"^/cookies/?$",      # Cookie policy
    r"^/terms/?$",        # Terms and conditions
    r"^/author/.*",       # Author pages
    r"^/tag/.*",          # Tag pages
    r"^/category/.*",     # Category pages
    r"^#.*"               # Anchor links
]

# Get logger for this module
logger = logging.getLogger('crawler')

def is_blacklisted(url: str) -> bool:
    """
    Check if a URL matches any blacklisted patterns.
    
    Args:
        url (str): The URL to check
        
    Returns:
        bool: True if URL is blacklisted, False otherwise
    """
    parsed = urlparse(url)
    path = parsed.path.lower().rstrip("/")
    
    normalized_path = path

    # Improved matching: exact path starts
    for pattern in BLACKLIST_PATTERNS:
        if re.match(pattern, normalized_path, re.IGNORECASE):
            return True

    return False

def extract_internal_links(links: Dict, base_domain: str, crawled_urls: Set[str]) -> List[str]:
    # Normalize base domain (remove www and protocol, convert to lowercase)
    normalized_base = base_domain.replace('www.', '').replace('http://', '').replace('https://', '').lower()
    
    # First, collect all valid internal links
    all_valid_links = []
    for link in links.get("internal", []):
        href = link.get("href")
        if href:
            absolute_url = urljoin(base_domain, href)
            
            # Normalize the URL for comparison (remove www and protocol)
            normalized_url = absolute_url.replace('www.', '').replace('http://', '').replace('https://', '').lower()
            
            if normalized_url.startswith(normalized_base) and absolute_url not in crawled_urls:
                all_valid_links.append(absolute_url)
    
    # Score and sort links by priority
    scored_links = []
    for link in all_valid_links:
        parsed = urlparse(link)
        path = parsed.path.lower()

        # Calculate priority score
        score = 0
        for pattern, weight in PRIORITY_PATTERNS:
            if re.search(pattern, path):
                score = weight
                break

        # If no priority pattern matches, give a default score of 1
        # This ensures we still include non-priority links
        if score == 0:
            score = 1

        scored_links.append((link, score))

    # Sort by score (highest first) and then alphabetically for same scores
    scored_links.sort(key=lambda x: (-x[1], x[0]))

    # Take top N links based on settings
    internal_links = [link for link, _ in scored_links[:settings.max_internal_links]]

    return internal_links

def clean_cookiebot_noise(md: str, ctx) -> str:
    """
    Remove Cookiebot-related content from markdown.
    
    Args:
        md (str): Markdown content
        ctx: Context object from crawler
        
    Returns:
        str: Cleaned markdown content
    """
    original_md = md

    # Remove common cookie banners by exact known sentences
    patterns = [
        r"This website uses cookies to ensure you get the best experience.*?\n",
        r"We use cookies to personalise content and ads.*?\n",
        r"In this context, it is possible that data may be transferred to insecure third countries.*?\n",
        r"\[\]\(https://www\.cookiebot\.com/en/what-is-behind-powered-by-cookiebot/\)",
        r"Consent Selection",
        r"\*\*Necessary\*\*.*?\*\*Marketing\*\*.*?Show details",
    ]

    for pattern in patterns:
        md = re.sub(pattern, "", md, flags=re.IGNORECASE | re.DOTALL)

    # Collapse multiple blank lines to two lines
    md = re.sub(r"\n{3,}", "\n\n", md)

    if len(md.strip()) < 0.5 * len(original_md.strip()):
        # If removing too much (more than 50%), revert to original
        return f"# Page: {ctx.url}\n\n{original_md.strip()}"

    return f"# Page: {ctx.url}\n\n{md.strip()}"

def reset_cache():
    """
    Reset the crawler's cache and state.
    """
    cached_pages.clear()
    existing_slugs.clear()

def make_slug(url: str) -> str:
    """
    Create a URL slug for caching.
    
    Args:
        url (str): URL to create slug from
        
    Returns:
        str: Generated slug
    """
    parsed = urlparse(url)
    path = parsed.path
    query = parsed.query

    # Try to use ObjectPath if present in query
    qs = parse_qs(query)
    if "ObjectPath" in qs and qs["ObjectPath"]:
        object_path = unquote(qs["ObjectPath"][0])
        slug_parts = [p for p in object_path.strip("/").split("/") if p]
    else:
        slug_parts = [p for p in path.strip("/").split("/") if p]

    if not slug_parts:
        slug = "index"
    else:
        slug = "_".join(slug_parts)

    # Clean weird characters and lowercase
    slug = re.sub(r"[^a-zA-Z0-9_]+", "_", slug).strip("_").lower()

    # Handle duplicate slugs
    if slug in existing_slugs:
        hash_suffix = hashlib.md5(url.encode()).hexdigest()[:8]
        slug = f"{slug}_{hash_suffix}"

    existing_slugs.add(slug)
    return slug

async def crawl_footer(crawler: AsyncWebCrawler, url: str):
    """
    Crawl website footer for additional content.
    
    Args:
        crawler (AsyncWebCrawler): Crawler instance
        url (str): URL to crawl footer from
    """
    try:
        logger.info(f"Crawling footer for {url}")
        config = CrawlerRunConfig(
            css_selector="footer",
            cache_mode=CacheMode.BYPASS,
            page_timeout=PAGE_TIMEOUT_MS,
            scan_full_page=True,
        )
        result = await crawler.arun(url=url, config=config)
        logger.debug(f"Footer crawl metadata: {result.metadata}")
        logger.debug(f"Footer crawl ID: {getattr(result, 'id', 'no-id')}")

        if not result.success:
            logger.warning(f"Footer crawl failed on {url}")
            return None

        if not result.markdown:
            logger.warning(f"No markdown content found in footer for {url}")
            return None

        logger.info(f"Successfully crawled footer for {url} (length: {len(result.markdown)} chars)")

        if "linkedin.com" in result.markdown.lower():
            logger.info(f"LinkedIn found in footer for {url}")
        else:
            logger.info(f"LinkedIn NOT found in footer for {url}")

        return result.markdown

    except Exception as e:
        logger.error(f"Error crawling footer for {url}: {str(e)}")
        return None

async def get_crawl_delay(base_url: str) -> float:
    """
    Get appropriate crawl delay for a domain.
    
    Args:
        base_url (str): Base URL to get delay for
        
    Returns:
        float: Crawl delay in seconds
    """
    parsed = urlparse(base_url)
    robots_url = f"{parsed.scheme}://{parsed.netloc}/robots.txt"

    if parsed.netloc in domain_crawl_delays:
        return domain_crawl_delays[parsed.netloc]

    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(robots_url, timeout=10) as response:
                if response.status != 200:
                    domain_crawl_delays[parsed.netloc] = CRAWL_DELAY_SECONDS
                    return CRAWL_DELAY_SECONDS

                text = await response.text()

                # Simple search for Crawl-delay
                match = re.search(r"Crawl-delay:\s*(\d+)", text, re.IGNORECASE)
                if match:
                    crawl_delay = int(match.group(1))
                    domain_crawl_delays[parsed.netloc] = crawl_delay
                    logger.info(f"Found crawl-delay for {parsed.netloc}: {crawl_delay} seconds")
                    return crawl_delay
                else:
                    domain_crawl_delays[parsed.netloc] = CRAWL_DELAY_SECONDS
                    return CRAWL_DELAY_SECONDS

    except Exception as e:
        logger.warning(f"Failed to fetch robots.txt for {base_url}: {e}")
        domain_crawl_delays[parsed.netloc] = CRAWL_DELAY_SECONDS
        return CRAWL_DELAY_SECONDS

async def cleanup_crawler(crawler: AsyncWebCrawler):
    """
    Clean up crawler resources.
    
    Args:
        crawler (AsyncWebCrawler): Crawler instance to clean up
    """
    try:
        if hasattr(crawler, 'context'):
            await crawler.context.close()
        if hasattr(crawler, 'browser'):
            await crawler.browser.close()
    except Exception as e:
        logger.warning(f"Error during crawler cleanup: {e}")

async def crawl_website(
    crawler: AsyncWebCrawler,
    url: str,
    base_domain: str,
    crawled_urls: Set[str],
    company_crawler: CompanyCrawler,
    depth: int = 0
):
    try:
        if url in crawled_urls or depth > MAX_DEPTH:
            return

        # @todo: shouldn't this check happen as very first step?
        if is_blacklisted(url):
            logger.warning(f"Skipping blacklisted page: {url}")
            return

        crawled_urls.add(url)
        if url in company_crawler.cached_pages:
            return

        config = CrawlerRunConfig(
            extraction_strategy=None,
            cache_mode=CacheMode.BYPASS,
            page_timeout=PAGE_TIMEOUT_MS
        )

        try:
            result = await crawler.arun(url=url, config=config)
        except Exception as crawl_error:
            company_crawler.failed_urls.append((url, f"crawler.arun error: {str(crawl_error)}"))
            return

        if not result.success:
            company_crawler.failed_urls.append((url, "result.success = False"))
            return


        cleaned_md = clean_cookiebot_noise(result.markdown, result)
        company_crawler.cached_pages[url] = cleaned_md

        # Generate slug for filename
        slug = company_crawler.make_slug(url)

        # Extract metadata from result
        metadata = {
            "title": result.metadata.get("title", ""),
            "description": result.metadata.get("description", ""),
            "url": url,
            "statusCode": result.status_code,
            "ogTitle": result.metadata.get("og:title", ""),
            "ogImage": result.metadata.get("og:image", ""),
            "sourceURL": url
        }

        # Save both markdown and JSON using utility
        if crawler.output_base_path:
            # Save to crawled_pages subdirectory
            page_path = f"crawled_pages/{slug}"
            save_page_content(
                base_path=crawler.output_base_path,
                slug=page_path,
                markdown=cleaned_md,
                metadata=metadata
            )

        internal_links = extract_internal_links(result.links, base_domain, crawled_urls) or []

        if internal_links:
            internal_config = CrawlerRunConfig(
                stream=True,  # Process results as they come in
                cache_mode=CacheMode.BYPASS,
                page_timeout=PAGE_TIMEOUT_MS
            )
            dispatcher = MemoryAdaptiveDispatcher(
                memory_threshold_percent=70.0,
                max_session_permit=5  # Limit concurrent sessions
            )

            try:
                async for result in await crawler.arun_many(
                    urls=internal_links,
                    config=internal_config,
                    dispatcher=dispatcher
                ):
                    if result.success:
                        if not is_blacklisted(result.url): # @todo: shouldn't this check happen as a very first step in the process?
                            company_crawler.cached_pages[result.url] = clean_cookiebot_noise(result.markdown, result)

                            # Extract more internal links if we haven't reached max depth
                            if depth < MAX_DEPTH:
                                # new_links = extract_internal_links(result.links, base_domain, crawled_urls)
                                await crawl_website(
                                    crawler=crawler,
                                    url=result.url,
                                    base_domain=base_domain,
                                    crawled_urls=crawled_urls,
                                    company_crawler=company_crawler,
                                    depth=depth + 1
                                )
                    else:
                        company_crawler.failed_urls.append(result.url)
            except Exception as arun_many_error:
                company_crawler.failed_urls.append((url, f"crawler.arun_many error: {str(arun_many_error)}"))

        # After all crawling is done, save all cached pages
        if depth == 0:  # Only at the root level
            save_cached_pages(crawler.output_base_path, company_crawler.cached_pages)

    except RecursionError as e:
        company_crawler.failed_urls.append((url, "RecursionError"))
        return
    except Exception as e:
        company_crawler.failed_urls.append((url, f"General error: {str(e)}"))
    finally:
        await cleanup_crawler(crawler)
