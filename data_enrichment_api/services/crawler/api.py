# data_enrichment_api/services/crawler/api.py
"""
FastAPI router for website crawling endpoints.
This module provides the API interface for the website crawler service.
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from urllib.parse import urlparse
from datetime import datetime
import os

from crawl4ai import AsyncWebCrawler
from data_enrichment_api.services.crawler.core import (
    CompanyCrawler,
    crawl_website,
    clean_cookiebot_noise
)

router = APIRouter()

class CrawlRequest(BaseModel):
    """
    Request model for website crawling.
    
    Attributes:
        url (str): URL of the website to crawl
        company_id (str): Unique identifier for the company
        company_name (str): Name of the company
    """
    url: str
    company_id: str
    company_name: str

class MarkdownResponse(BaseModel):
    """
    Response model for crawled markdown content.
    
    Attributes:
        markdown (str): Crawled markdown content
        total_pages (int): Number of pages crawled
    """
    markdown: str
    total_pages: int

@router.post("/crawl", response_model=MarkdownResponse)
async def crawl_endpoint(request: CrawlRequest):
    """
    Crawl a website and return its content as markdown.
    
    Args:
        request (CrawlRequest): Request containing the URL to crawl
        
    Returns:
        MarkdownResponse: Response containing crawled markdown and page count
        
    Raises:
        HTTPException: If URL is invalid or crawling fails
    """
    try:
        parsed = urlparse(request.url)
        base_domain = f"{parsed.scheme}://{parsed.netloc}"
        domain = parsed.netloc  # keep dots
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_base_path = os.path.join("outputs", domain, timestamp)

        # Create company-specific crawler
        company_crawler = CompanyCrawler(
            company_id=request.company_id,
            company_name=request.company_name
        )
        company_crawler.reset_cache() # @todo: check if this call needed here right after new instance of `CompanyCrawler`

        async with AsyncWebCrawler(
            allowed_domains=[parsed.netloc],
            after_markdown_hook=clean_cookiebot_noise
        ) as crawler:
            crawler.output_base_path = output_base_path
            await crawl_website(
                crawler=crawler,
                url=request.url,
                base_domain=base_domain,
                crawled_urls=company_crawler.crawled_urls,
                company_crawler=company_crawler
            )

        markdown = company_crawler.get_cached_markdown_dump()
        return MarkdownResponse(
            markdown=markdown,
            total_pages=len(company_crawler.crawled_urls)
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
