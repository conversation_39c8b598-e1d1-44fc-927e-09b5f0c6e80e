# data_enrichment_api/orchestrator_parallel.py
"""
Parallel company enrichment orchestrator.
This module provides the orchestration layer for parallel company enrichment,
handling multiple companies simultaneously while maintaining isolation.
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Optional
from urllib.parse import urlparse
from datetime import datetime
from google.cloud import storage
import asyncio, logging, os, csv

from data_enrichment_api.services.validator.core import validate_company_url
from data_enrichment_api.services.crawler.core import CompanyCrawler, crawl_website
from data_enrichment_api.services.content_cleaner.core import clean_markdown_pages
from data_enrichment_api.services.company_profiler.multimodel_runner import MultiModelRunner
from data_enrichment_api.utils.file_store import save_enriched_output
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig, CacheMode, MemoryAdaptiveDispatcher
from data_enrichment_api.utils.db_saver import save_enriched_company, save_invalid_url

# Configure logger
logger = logging.getLogger('orchestrator_parallel')

router = APIRouter()

class CompanyInput(BaseModel):
    """
    Input model for company data.
    
    Attributes:
        deal_id (str): Deal ID of the company
        company_name (str): Name of the company
        website (str): Website URL of the company
    """
    deal_id: str
    company_name: str
    website: str

class ParallelEnrichRequest(BaseModel):
    """
    Request model for parallel company enrichment.
    
    Attributes:
        companies (List[CompanyInput]): List of companies to process
    """
    companies: List[CompanyInput]

class CompanyResult(BaseModel):
    """
    Result model for a single company's enrichment.
    
    Attributes:
        deal_id (str): Deal ID of the company
        company_name (str): Name of the company
        status (str): Status of the enrichment ('success', 'error')
        error (Optional[str]): Error message if status is 'error'
        profiles (Optional[Dict]): Enriched profiles if status is 'success'
        validation (Optional[Dict]): Validation result for the URL
        validation_notes (Optional[str]): Detailed validation notes
    """
    deal_id: str
    company_name: str
    status: str
    error: Optional[str] = None
    profiles: Optional[Dict] = None
    validation: Optional[Dict] = None
    validation_notes: Optional[str] = None

async def process_single_company(company: CompanyInput, crawler: AsyncWebCrawler) -> CompanyResult:
    try:
        validation_result = validate_company_url(company.company_name, company.website)

        if not validation_result["is_valid_url"]:
            save_invalid_url(
                base_company={
                    "deal_id": company.deal_id,
                    "company_id": None,
                    "company_name": company.company_name,
                    "created_at": datetime.utcnow(),
                    "website": company.website
                },
                validation_notes=validation_result["validation_notes"]
            )

            return CompanyResult(
                deal_id=company.deal_id,
                company_name=company.company_name,
                status="error",
                error=f"URL validation failed: {validation_result['validation_notes']}",
                validation=validation_result,
                validation_notes=validation_result["validation_notes"]
            )

        company_crawler = CompanyCrawler(
            company_id=company.deal_id,
            company_name=company.company_name
        )

        # Step 3: Crawl website using the provided crawler instance
        parsed = urlparse(company.website)
        base_domain = f"{parsed.scheme}://{parsed.netloc}"
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        domain_name = parsed.netloc.replace("www.", "")
        output_base_path = os.path.join("outputs", domain_name, timestamp)

        # Actually crawl the website using the provided crawler
        crawler.output_base_path = output_base_path
        await crawl_website(
            crawler=crawler,
            url=company.website,
            base_domain=base_domain,
            crawled_urls=company_crawler.crawled_urls,
            company_crawler=company_crawler
        )

        # Get crawled content
        raw_pages = {}
        for url, content in company_crawler.cached_pages.items():
            page_path = urlparse(url).path.strip("/") or "index"
            if isinstance(content, str):
                markdown = content
            else:
                markdown = content.get("markdown", "")

            if markdown:
                raw_pages[page_path] = markdown.strip()

        if not raw_pages:
            return CompanyResult(
                deal_id=company.deal_id,
                company_name=company.company_name,
                status="error",
                error="No content crawled"
            )

        # Step 4: Clean content
        cleaned_pages = await clean_markdown_pages(raw_pages)
        cleaned_text = "\n\n".join(cleaned_pages.values())

        # Step 5: Generate profiles
        runner = MultiModelRunner()
        model_outputs = await runner.run_all_models(cleaned_text, company.company_name)

        # Step 6: Save results to GCS
        metadata = {
            "company_name": company.company_name,
            "deal_id": company.deal_id,
            "domain": domain_name,
            "timestamp": timestamp,
            "validation_result": validation_result
        }

        await save_enriched_output(
            output_base_path=output_base_path,
            raw_data=raw_pages,
            cleaned_data=cleaned_pages,
            profile_data=model_outputs,
            metadata=metadata
        )
        
        # Step 7: Save results to database
        try:
            save_enriched_company(
                base_company={
                    "deal_id": company.deal_id,
                    "company_id": None,  # Set company_id to NULL
                    "company_name": company.company_name,
                    "created_at": datetime.utcnow(),
                    "website": company.website
                },
                model_profiles=model_outputs
            )
        except Exception as db_error:
            logger.error(f"Failed to save to database for {company.company_name}: {str(db_error)}")
            # Continue with the process even if database save fails
            # The data is still saved in GCS
        
        logger.info(f"Successfully completed processing for {company.company_name}")
        return CompanyResult(
            deal_id=company.deal_id,
            company_name=company.company_name,
            status="success",
            profiles=model_outputs,
            validation=validation_result,
            validation_notes=validation_result.get("validation_notes", "URL is valid")
        )

    except Exception as e:
        return CompanyResult(
            deal_id=company.deal_id,
            company_name=company.company_name,
            status="error",
            error=str(e)
        )

# @router.post("/enrich-parallel", response_model=List[CompanyResult])
async def enrich_parallel(request: ParallelEnrichRequest):
    config = CrawlerRunConfig(
        stream=True,  # Process results as they come in
        cache_mode=CacheMode.BYPASS,
        page_timeout=30000  # 30-seconds timeout per company
    )

    async with AsyncWebCrawler() as crawler:
        company_tasks = []
        active_companies = set()
        valid_results = []  # <-- New list to store valid results

        async for result in await crawler.arun_many(
            urls=[company.website for company in request.companies],
            config=config,
        ):
            ### @todo: is it possible that we will not have a valid company entry here?
            # Find the corresponding company input
            company = next((c for c in request.companies if c.website == result.url), None)

            if not company:
                logger.error(f"Could not find company for URL: {result.url}")
                continue
            ### @todo: ----------------------------------------------------------------------------------------------

            validation_result = validate_company_url(company.company_name, company.website)

            if not validation_result["is_valid_url"]:
                try:
                    save_invalid_url(
                        base_company={
                            "deal_id": company.deal_id,
                            "company_id": None,
                            "company_name": company.company_name,
                            "created_at": datetime.utcnow(),
                            "website": company.website
                        },
                        validation_notes=validation_result["validation_notes"]
                    )
                except Exception as db_error:
                    pass # @todo: or should it `rase` here?

                error_result = CompanyResult(
                    deal_id=company.deal_id,
                    company_name=company.company_name,
                    status="error",
                    error=f"URL validation failed: {validation_result['validation_notes']}",
                    validation=validation_result,
                    validation_notes=validation_result["validation_notes"]
                )
                valid_results.append(error_result)
                continue

            if result.success:
                active_companies.add(company.company_name)
                company_tasks.append( asyncio.create_task(process_single_company(company, crawler)) )
            else:
                error_result = CompanyResult(
                    deal_id=company.deal_id,
                    company_name=company.company_name,
                    status="error",
                    error=f"Failed to crawl website: {result.error_message}"
                )
                valid_results.append(error_result)
                company_tasks.append(asyncio.create_task(asyncio.sleep(0))) # @todo: is it necessary at all? what will happen if we'll try to `asyncio.gather(*[])` on empty list ?

        results = await asyncio.gather(*company_tasks, return_exceptions=True)

        valid_results = []
        for result in results:
            if isinstance(result, Exception):
                continue

            valid_results.append(result)

    return valid_results

# @router.get("/unprocessed-companies")
async def get_unprocessed_companies_endpoint():
    """
    Get a list of all unprocessed companies from GCS.
    
    Returns:
        dict: Dictionary containing list of unprocessed companies
    """
    try:
        project_id = os.getenv("PROJECT_ID")
        bucket_name = os.getenv("GCS_INPUT_BUCKET_NAME")
        blob_path = os.getenv("UNPROCESSED_COMPANIES_PATH")

        # Get the unprocessed companies CSV from GCS
        gcs_client = storage.Client(project=project_id)
        bucket = gcs_client.bucket(bucket_name=bucket_name)
        blob = bucket.blob(blob_path)  # Fixed path
        if not blob.exists():
            logger.error("CSV file not found in GCS")
            return {
                "status": "error",
                "message": "CSV file not found in GCS"
            }

        # Download and parse the CSV
        content = blob.download_as_text()
        companies = []

        # Parse CSV content
        reader = csv.DictReader(content.splitlines())
        for row in reader:
            companies.append({
                "deal_id": row["deal_id"],
                "company_name": row["company_name"],
                "website": row["website"]
            })

        return {
            "status": "success",
            "count": len(companies),
            "companies": companies
        }
    except Exception as e:
        logger.error(f"Error reading unprocessed companies from GCS: {str(e)}")
        return {
            "status": "error",
            "message": str(e)
        }
