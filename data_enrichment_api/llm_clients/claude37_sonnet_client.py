# data_enrichment_api/llm_clients/claude37_sonnet_client.py

import logging, os, asyncio
from anthropic import Async<PERSON>nthropic
from .base import LLMClientInterface

# Configure logger
logger = logging.getLogger('claude37_sonnet_client')

class Claude37SonnetClient(LLMClientInterface):
    """
    Client for Anthropic's Claude 3.7 Sonnet model.
    
    This client implements the LLMClientInterface to provide consistent behavior
    with other language model clients while using Claude 3.7 Sonnet's specific API.
    """
    
    def __init__(self, timeout: int = 60):
        """
        Initialize the Claude 3.7 Sonnet client.
        
        Sets up the Anthropic client with the API key from environment variables
        and configures the model version.

        Args:
            timeout (int): Timeout in seconds for API calls (default: 60)
        """
        self.client = AsyncAnthropic(api_key=os.getenv("ANTHROPIC_API_KEY"))
        self.model = "claude-3-7-sonnet-20250219"
        self.timeout = timeout

    async def run(self, prompt: str, system_prompt: str, max_tokens: int = 1800, max_retries: int = 3) -> dict:
        """
        Execute a prompt with Claude 3.7 Sonnet.
        
        Args:
            prompt (str): The main prompt to send to the model
            system_prompt (str): The system-level instructions for the model
            max_tokens (int): Maximum number of tokens to generate (default: 1800)
            max_retries (int): Maximum number of retries on failure (default: 3)

        Returns:
            dict: Dictionary containing the model's response and token usage
        """
        for attempt in range(max_retries):
            try:
                async with asyncio.timeout(self.timeout):
                    response = await self.client.messages.create(
                        model=self.model,
                        system=system_prompt,
                        messages=[{"role": "user", "content": prompt}],
                        max_tokens=max_tokens,
                        temperature=0.0,
                    )
                    content = response.content[0].text.strip()
                    usage = response.usage
                    return {
                        "content": content,
                        "usage": {
                            "prompt_tokens": usage.input_tokens,
                            "completion_tokens": usage.output_tokens
                        }
                    }
            except asyncio.TimeoutError:
                if attempt == max_retries - 1:
                    logger.error(f"Request timed out after {self.timeout} seconds (attempt {attempt + 1}/{max_retries})")
                    raise
                logger.warning(f"Request timed out, retrying... (attempt {attempt + 1}/{max_retries})")
                await asyncio.sleep(1)  # Wait before retrying
            except Exception as e:
                if attempt == max_retries - 1:
                    logger.error(f"Error running Claude 3.7 Sonnet: {e}")
                    raise
                logger.warning(f"Error occurred, retrying... (attempt {attempt + 1}/{max_retries})")
                await asyncio.sleep(1)  # Wait before retrying
