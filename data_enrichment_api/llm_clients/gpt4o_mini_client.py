# data_enrichment_api/llm_clients/gpt4o_mini_client.py

"""
GPT-4o Mini client implementation.
This module provides a client for interacting with OpenAI's GPT-4o Mini model,
implementing the LLMClientInterface for consistent behavior with other models.
"""

import logging, os, asyncio
from openai import Async<PERSON>penA<PERSON>
from .base import LLMClientInterface

# Configure logger
logger = logging.getLogger('gpt4o_mini_client')

class GPT4OMiniClient(LLMClientInterface):
    """
    Client for OpenAI's GPT-4o Mini model.
    
    This client implements the LLMClientInterface to provide consistent behavior
    with other language model clients while using GPT-4o Mini's specific API.
    """
    
    def __init__(self, timeout: int = 60):
        """
        Initialize the GPT-4o Mini client.
        
        Sets up the OpenAI client with the API key from environment variables
        and configures the model version.

        Args:
            timeout (int): Timeout in seconds for API calls (default: 60)
        """
        self.client = AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.model = "gpt-4o-mini"
        self.timeout = timeout

    async def run(self, prompt: str, system_prompt: str, max_tokens: int = 1800, max_retries: int = 3) -> dict:
        """
        Execute a prompt with GPT-4o Mini.
        
        Args:
            prompt (str): The main prompt to send to the model
            system_prompt (str): The system-level instructions for the model
            max_tokens (int): Maximum number of tokens to generate (default: 1800)
            max_retries (int): Maximum number of retries on failure (default: 3)

        Returns:
            dict: Dictionary containing the model's response and token usage
        """
        for attempt in range(max_retries):
            try:
                async with asyncio.timeout(self.timeout):
                    response = await self.client.chat.completions.create(
                        model=self.model,
                        messages=[
                            {"role": "system", "content": system_prompt},
                            {"role": "user", "content": prompt}
                        ],
                        temperature=0.0,
                        max_tokens=max_tokens
                    )
                    content = response.choices[0].message.content.strip()
                    usage = response.usage
                    return {
                        "content": content,
                        "usage": {
                            "prompt_tokens": usage.prompt_tokens,
                            "completion_tokens": usage.completion_tokens
                        }
                    }
            except asyncio.TimeoutError:
                if attempt == max_retries - 1:
                    logger.error(f"Request timed out after {self.timeout} seconds (attempt {attempt + 1}/{max_retries})")
                    raise
                logger.warning(f"Request timed out, retrying... (attempt {attempt + 1}/{max_retries})")
                await asyncio.sleep(1)  # Wait before retrying
            except Exception as e:
                if attempt == max_retries - 1:
                    logger.error(f"Error running GPT-4o Mini: {e}")
                    raise
                logger.warning(f"Error occurred, retrying... (attempt {attempt + 1}/{max_retries})")
                await asyncio.sleep(1)  # Wait before retrying
