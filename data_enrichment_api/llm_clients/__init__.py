# data_enrichment_api/llm_clients/__init__.py

"""
LLM Clients Package Initialization.
This module provides a factory function to create LLM client instances based on model names.
It serves as the main entry point for accessing different language model implementations.
"""

import logging
from .gpt4o_mini_client import GPT<PERSON><PERSON>ini<PERSON><PERSON>
from .gpt41_nano_client import GPT41Nano<PERSON>lient
from .gpt41_flagship_client import GPT41<PERSON>lag<PERSON><PERSON>lient
from .gpt41_mini_client import GPT41MiniClient 
from .gpt_o3_client import GP<PERSON>3<PERSON>lient
from .gpt_o4_mini_client import GPTO4MiniClient
from .claude37_sonnet_client import Claude37SonnetClient
from .claude35_sonnet_client import Claude35SonnetClient
from .claude3_haiku_client import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
#from .grok3_mini_beta_client import Grok3MiniBetaClient

# Configure logger
logger = logging.getLogger('llm_clients')

def get_llm_client(model_name: str):
    """
    Factory function to create an LLM client instance based on the model name.
    
    This function maps model names to their corresponding client classes and
    returns an initialized client instance. It provides a centralized way to
    create LLM clients while abstracting the implementation details.
    
    Args:
        model_name (str): The name of the model to create a client for
        
    Returns:
        LLMClientInterface: An instance of the requested LLM client
        
    Raises:
        ValueError: If the requested model is not supported
    """
    model_map = {
        "gpt-4o-mini": GPT4OMiniClient,
        "gpt-4.1-nano": GPT41NanoClient,
        "gpt-4.1": GPT41FlagshipClient,
        "gpt-4.1-mini": GPT41MiniClient,
        "gpt-o3": GPTO3Client,
        "o4-mini": GPTO4MiniClient,
        "claude-3-7-sonnet-20250219": Claude37SonnetClient,
        "claude-3-5-sonnet-20240620": Claude35SonnetClient,
        "claude-3-haiku-20240307": Claude3HaikuClient#,
        #"grok-3-mini-beta": Grok3MiniBetaClient,
    }

    client_class = model_map.get(model_name)
    if not client_class:
        logger.error(f"Unsupported model requested: {model_name}")
        raise ValueError(f"Unsupported model: {model_name}")
    
    logger.info(f"Creating client for model: {model_name}")
    return client_class()
