# data_enrichment_api/llm_clients/claude37_sonnet_client.py

from anthropic import As<PERSON><PERSON><PERSON>hropic
from .base import LLMClientInterface
import os

class Claude3HaikuClient(LLMClientInterface):
    def __init__(self):
        self.client = AsyncAnthropic(api_key=os.getenv("ANTHROPIC_API_KEY"))
        self.model = "claude-3-haiku-20240307"

    async def run(self, prompt: str, system_prompt: str, max_tokens: int = 1800) -> dict:
        response = await self.client.messages.create(
            model=self.model,
            system=system_prompt,
            messages=[{"role": "user", "content": prompt}],
            max_tokens=max_tokens,
            temperature=0.0,
        )
        content = response.content[0].text.strip()
        usage = response.usage
        return {
            "content": content,
            "usage": {
                "prompt_tokens": usage.input_tokens,
                "completion_tokens": usage.output_tokens
            }
        }
