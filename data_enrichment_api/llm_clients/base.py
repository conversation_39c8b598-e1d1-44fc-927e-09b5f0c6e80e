# data_enrichment_api/llm_clients/base.py

"""
Base interface for LLM clients.
This module defines the abstract base class for all LLM client implementations,
ensuring consistent interface across different language models.
"""

from abc import ABC, abstractmethod

class LLMClientInterface(ABC):
    """
    Abstract base class for LLM clients.
    
    This interface defines the contract that all LLM client implementations must follow.
    It ensures consistent behavior across different language model providers.
    
    Methods:
        run: Asynchronously execute a prompt with the language model
    """
    @abstractmethod
    async def run(self, prompt: str, system_prompt: str, max_tokens: int = 1800) -> str:
        """
        Execute a prompt with the language model.
        
        Args:
            prompt (str): The main prompt to send to the model
            system_prompt (str): The system-level instructions for the model
            max_tokens (int): Maximum number of tokens to generate (default: 1800)
            
        Returns:
            str: The model's response
        """
        pass
