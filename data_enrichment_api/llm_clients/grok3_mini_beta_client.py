# data_enrichment_api/llm_clients/grok3_mini_beta_client.py

from openai import AsyncOpenAI
from .base import LLMClientInterface
import os

class Grok3MiniBetaClient(LLMClientInterface):
    def __init__(self):
        self.client = AsyncOpenAI(
            api_key=os.getenv("XAI_API_KEY"),
            base_url="https://api.x.ai/v1"
        )
        self.model = "grok-3-mini-beta"

    async def run(self, prompt: str, system_prompt: str, max_tokens: int = 1800) -> dict:
        response = await self.client.chat.completions.create(
            model=self.model,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ],
            temperature=0.0,
            max_tokens=max_tokens
        )
        content = response.choices[0].message.content.strip()
        usage = response.usage
        return {
            "content": content,
            "usage": {
                "prompt_tokens": usage.prompt_tokens,
                "completion_tokens": usage.completion_tokens
            }
        }
