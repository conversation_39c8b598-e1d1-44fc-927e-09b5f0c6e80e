# data_enrichment_api/orchestrator_multi.py
"""
Multi-model company enrichment orchestrator.
This module provides the orchestration layer for the company enrichment pipeline,
supporting multiple LLM models and handling the complete enrichment workflow.
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from urllib.parse import urlparse
from crawl4ai import AsyncWebCrawler
from data_enrichment_api.services.validator.core import validate_company_url
from data_enrichment_api.services.crawler.core import crawl_website, reset_cache, cached_pages
from data_enrichment_api.services.content_cleaner.core import clean_markdown_pages
from data_enrichment_api.services.company_profiler.multimodel_runner import MultiModelRunner
from data_enrichment_api.utils.token_utils import count_tokens_in_dict
from data_enrichment_api.utils.file_store import save_enriched_output
from data_enrichment_api.utils.db import (
    fetch_companies_to_validate,
    get_company_by_deal_id,
    is_deal_id_processed,
    get_unprocessed_companies
)
from data_enrichment_api.utils.db_saver import save_enriched_company, save_invalid_url
import os
import logging
from datetime import datetime

# Configure logger
logger = logging.getLogger('orchestrator_multi')

router = APIRouter()

class EnrichRequest(BaseModel):
    """
    Request model for company enrichment.
    
    Attributes:
        company_name (str): Name of the company to enrich
        url (str): Website URL of the company
    """
    company_name: str
    url: str

class CompanyIDRequest(BaseModel):
    """
    Request model for company enrichment by deal ID.
    
    Attributes:
        deal_id (str): Deal identifier for the company
    """
    deal_id: str

@router.post("/enrich-multi")
async def enrich_multi(request: EnrichRequest):
    """
    Enrich a company using multiple LLM models.
    
    This endpoint performs the complete enrichment pipeline:
    1. Validates the company URL
    2. Crawls the website
    3. Cleans the crawled content
    4. Generates profiles using multiple LLM models
    5. Saves the results
    
    Args:
        request (EnrichRequest): Request containing company name and URL
        
    Returns:
        dict: Enriched profiles by model or error information
    """
    reset_cache()
    parsed = urlparse(request.url)
    base_domain = f"{parsed.scheme}://{parsed.netloc}"
    allowed_domain = parsed.netloc
    crawled_urls = set()

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    domain_name = parsed.netloc.replace("www.", "")
    output_base_path = os.path.join("outputs", domain_name, timestamp)
    # os.makedirs(output_base_path, exist_ok=True)  # Commented out since we're using GCS only

    # Step 0: Validate company and URL
    validation_result = validate_company_url(request.company_name, request.url)
    if not validation_result["is_valid_url"]:
        logger.info(f"Validation failed for: {request.url} ({validation_result['validation_notes']})")
        return {
            "error": "URL validation failed",
            "validation": validation_result
        }

    # Step 1: Crawl
    async with AsyncWebCrawler(allowed_domains=[allowed_domain]) as crawler:
        crawler.output_base_path = output_base_path
        await crawl_website(crawler, request.url, base_domain, crawled_urls, company_name=request.company_name)

    raw_pages = {
        urlparse(url).path.strip("/") or "index": content
        for url, content in cached_pages.items()
    }
    raw_token_count = count_tokens_in_dict(raw_pages)

    # Step 2: Clean
    cleaned_pages = await clean_markdown_pages(raw_pages)
    cleaned_token_count = count_tokens_in_dict(cleaned_pages)

    # Step 3: Multi-model profiling
    combined_text = "\n\n".join(cleaned_pages.values())
    runner = MultiModelRunner()
    model_outputs = await runner.run_all_models(combined_text, request.company_name)

    # Step 4: Save files to GCS
    metadata = {
        "company_name": request.company_name,
        "raw_tokens": raw_token_count,
        "cleaned_tokens": cleaned_token_count,
        "tokens_saved": raw_token_count - cleaned_token_count,
        "validation_result": validation_result,
        "domain": domain_name,
        "timestamp": timestamp
    }

    await save_enriched_output(
        output_base_path=output_base_path,
        raw_data=raw_pages,
        cleaned_data=cleaned_pages,
        profile_data=model_outputs,
        metadata=metadata
    )

    logger.info(f"Enrichment complete for {domain_name} with multiple models.")
    reset_cache()

    return {
        "profiles_by_model": model_outputs
    }

def get_settings():
    """
    Get the current settings by creating a new instance.
    
    Returns:
        Settings: Current application settings
    """
    from data_enrichment_api.config import Settings
    return Settings()

def update_env_file(start_id: int):
    """
    Update the AUTO_ENRICH_START_ID in the .env file.
    
    Args:
        start_id (int): New value for AUTO_ENRICH_START_ID
    """
    env_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), '.env')
    with open(env_path, 'r') as f:
        lines = f.readlines()
    
    with open(env_path, 'w') as f:
        for line in lines:
            if line.startswith('AUTO_ENRICH_START_ID='):
                f.write(f'AUTO_ENRICH_START_ID={start_id}\n')
            else:
                f.write(line)

@router.post("/auto-enrich")
async def auto_enrich_from_db():
    """
    Automatically fetch one row from the database and run the enrichment pipeline.
    
    This endpoint:
    1. Fetches the next company to process from the database
    2. Validates the company URL
    3. Performs the enrichment pipeline
    4. Saves results to the database
    5. Updates the AUTO_ENRICH_START_ID for the next run
    
    Returns:
        dict: Enrichment results or error information
    """
    settings = get_settings()
    logger.info(f"Starting with AUTO_ENRICH_START_ID: {settings.AUTO_ENRICH_START_ID}")
    logger.info("Fetching companies from database...")
    companies = fetch_companies_to_validate(
        start_id=settings.AUTO_ENRICH_START_ID,
        limit=settings.AUTO_ENRICH_LIMIT
    )
    if not companies:
        logger.error("No companies found in the database")
        return {"error": "No companies found in the database."}

    company = companies[0]
    logger.info(f"Processing company: {company['company_name']}")
    logger.info(f"Company data: {company}")
    
    base_company = {
        "deal_id":    company["deal_id"],
        "company_id": company["company_id"],
        "company_name": company["company_name"],
        "created_at": company.get("created_at")
    }

    # 1️⃣ Validate URL up front
    validation_result = validate_company_url(company["company_name"], company["website"])
    logger.info(f"Validation result: {validation_result}")

    if not validation_result["is_valid_url"]:
        logger.error(f"URL validation failed for {company['company_name']}")
        logger.error(f"Validation notes: {validation_result['validation_notes']}")

        # Save exactly one "invalid" row and quit
        try:
            logger.info(f"Attempting to save invalid URL to database for {company['company_name']}")
            save_invalid_url(base_company, validation_result["validation_notes"])
            logger.info(f"Successfully saved invalid URL to database for {company['company_name']}")
        except Exception as db_error:
            logger.error(f"Failed to save invalid URL to database: {str(db_error)}")
            logger.error(f"Full error details: {str(db_error)}")
            raise

        return {
            "error": "Invalid URL",
            "validation": validation_result,
            "validation_notes": validation_result["validation_notes"]
        }

    # 2️⃣ Otherwise kick off the normal enrichment
    logger.info(f"Starting enrichment for {company['company_name']}")
    request = EnrichRequest(
        company_name=company["company_name"],
        url=company["website"]
    )
    
    try:
        result = await enrich_multi(request)

        if not result.get("profiles_by_model"):
            logger.warning("No profiles to save. Skipping DB save.")
            return {"error": "No enriched profiles found."}

        # 3️⃣ Save the 9 LLM‐model rows
        save_enriched_company(
            base_company={**base_company, "website": company["website"]},
            model_profiles=result["profiles_by_model"]
        )

        # 4️⃣ Update the AUTO_ENRICH_START_ID in .env file
        next_id = company["company_id"] + 1
        update_env_file(next_id)
        logger.info(f"Updated AUTO_ENRICH_START_ID to {next_id}")

        return {
            "company_id":    company["company_id"],
            "company_name":  company["company_name"],
            "url":           company["website"],
            "saved_models":  list(result["profiles_by_model"].keys()),
            "next_company_id": next_id
        }
    except Exception as e:
        logger.error(f"Error during enrichment: {e}")
        return {"error": str(e)}
    finally:
        # Ensure cache is reset for next run
        reset_cache()

@router.post("/enrich-company")
async def enrich_single_company(request: CompanyIDRequest):
    """
    Enrich a single company by its deal_id.
    
    This endpoint:
    1. Takes a deal_id
    2. Checks if the deal_id has already been processed
    3. If not processed, fetches company data from the database
    4. Performs validation and enrichment
    5. Saves results to the database
    6. Cleans up and returns the results
    
    Args:
        request (CompanyIDRequest): Request containing the deal ID
        
    Returns:
        dict: Enrichment results or error information
    """
    logger.info(f"Processing deal ID: {request.deal_id}")
    
    # Check if deal_id has already been processed
    if is_deal_id_processed(request.deal_id):
        logger.info(f"Deal ID {request.deal_id} has already been processed. Skipping.")
        return {
            "deal_id": request.deal_id,
            "status": "skipped",
            "message": "Deal ID has already been processed"
        }
    
    # Fetch company data from database
    company = get_company_by_deal_id(request.deal_id)
    if not company:
        logger.error(f"Company not found with deal ID: {request.deal_id}")
        return {"error": f"Company not found with deal ID: {request.deal_id}"}

    logger.info(f"Processing company: {company['company_name']}")
    logger.info(f"Company data: {company}")
    
    base_company = {
        "deal_id":    company["deal_id"],
        "company_id": company["company_id"],
        "company_name": company["company_name"],
        "created_at": company.get("created_at")
    }

    # 1️⃣ Validate URL up front
    validation_result = validate_company_url(company["company_name"], company["website"])
    logger.info(f"Validation result: {validation_result}")

    if not validation_result["is_valid_url"]:
        logger.error(f"URL validation failed for {company['company_name']}")
        logger.error(f"Validation notes: {validation_result['validation_notes']}")

        # Save exactly one "invalid" row and quit
        try:
            logger.info(f"Attempting to save invalid URL to database for {company['company_name']}")
            save_invalid_url(base_company, validation_result["validation_notes"])
            logger.info(f"Successfully saved invalid URL to database for {company['company_name']}")
        except Exception as db_error:
            logger.error(f"Failed to save invalid URL to database: {str(db_error)}")
            logger.error(f"Full error details: {str(db_error)}")
            raise

        return {
            "error": "Invalid URL",
            "validation": validation_result,
            "validation_notes": validation_result["validation_notes"]
        }

    # 2️⃣ Otherwise kick off the normal enrichment
    logger.info(f"Starting enrichment for {company['company_name']}")
    enrich_request = EnrichRequest(
        company_name=company["company_name"],
        url=company["website"]
    )
    
    try:
        result = await enrich_multi(enrich_request)

        if not result.get("profiles_by_model"):
            logger.warning("No profiles to save. Skipping DB save.")
            return {"error": "No enriched profiles found."}

        # 3️⃣ Save the 9 LLM‐model rows
        save_enriched_company(
            base_company={**base_company, "website": company["website"]},
            model_profiles=result["profiles_by_model"]
        )

        return {
            "deal_id":      company["deal_id"],
            "company_id":   company["company_id"],
            "company_name": company["company_name"],
            "url":          company["website"],
            "saved_models": list(result["profiles_by_model"].keys()),
            "status": "success"
        }
    except Exception as e:
        logger.error(f"Error during enrichment: {e}")
        return {"error": str(e)}
    finally:
        # Ensure cache is reset for next run
        reset_cache()

@router.get("/unprocessed-companies")
async def get_unprocessed_companies_endpoint():
    """
    Get a list of all unprocessed companies.
    
    Returns:
        dict: Dictionary containing list of unprocessed companies
    """
    try:
        companies = await get_unprocessed_companies()
        return {
            "status": "success",
            "count": len(companies),
            "companies": companies
        }
    except Exception as e:
        logger.error(f"Error in get_unprocessed_companies endpoint: {str(e)}")
        return {
            "status": "error",
            "message": str(e)
        }
