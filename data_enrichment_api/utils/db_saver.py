# data_enrichment_api/utils/db_saver.py
"""
Database saving utilities for company enrichment data.
This module provides functionality for saving company profiles and validation results
to the database, including handling of invalid URLs and multi-model profiles.
"""

import logging, json
from sqlalchemy import text
from data_enrichment_api.utils.db import engine, get_db_connection
from datetime import datetime

# Configure logger
logger = logging.getLogger('db_saver')

def safe_json_dumps(value):
    """
    Safely convert a value to JSON string.
    
    Args:
        value: Value to convert to JSON
        
    Returns:
        str: JSON string representation of the value, or None if value is None
    """
    if value is None:
        return None
    return json.dumps(value, ensure_ascii=False)

def save_invalid_url(base_company: dict, validation_notes: str):
    """
    Save a company with an invalid URL to the database.
    
    Args:
        base_company (dict): Basic company information containing:
            - deal_id: Deal identifier
            - company_id: Company identifier
            - company_name: Name of the company
            - website: Original website URL
            - created_at: Optional creation timestamp
        validation_notes (str): Notes about why the URL is invalid
        
    Raises:
        Exception: If database operation fails
    """
    try:
        logger.info(f"Starting to save invalid URL for company: {base_company['company_name']}")
        logger.info(f"Company details: deal_id={base_company['deal_id']}, website={base_company['website']}")
        logger.info(f"Validation notes: {validation_notes}")

        insert = text("""
          INSERT INTO da_company_enrichment_data (
            deal_id, company_id, company_name, website,
            validation_notes, created_at, updated_at
          )
          VALUES (
            :deal_id, :company_id, :company_name, :website,
            :validation_notes, :created_at, :updated_at
          )
        """)

        # Ensure the website is present and not None
        if 'website' not in base_company or base_company['website'] is None:
            logger.error(f"Missing website URL for company {base_company['company_name']}")
            raise ValueError("Website URL is required")

        params = {
            "deal_id": base_company["deal_id"],
            "company_id": base_company["company_id"],
            "company_name": base_company["company_name"],
            "website": base_company["website"],  # Use the original website URL
            "validation_notes": validation_notes,
            "created_at": base_company.get("created_at") or datetime.utcnow(),
            "updated_at": datetime.utcnow(),
        }

        logger.info(f"Executing database insert with params: {params}")

        # Use a single transaction for the entire operation
        with get_db_connection() as conn:
            with conn.begin():
                try:
                    result = conn.execute(insert, params)
                    logger.info(f"Successfully saved invalid URL for company: {base_company['company_name']}")
                    logger.info(f"Database operation result: {result.rowcount} rows affected")
                except Exception as e:
                    logger.error(f"Database error while saving invalid URL: {str(e)}")
                    logger.error(f"SQL: {insert}")
                    logger.error(f"Params: {params}")
                    raise  # Re-raise to ensure transaction rollback
    except Exception as e:
        logger.error(f"Failed to save invalid URL for company {base_company['company_name']}: {e}")
        logger.error(f"Full error details: {str(e)}")
        raise  # Re-raise to ensure the error is propagated

def save_enriched_company(base_company: dict, model_profiles: dict):
    """
    Save enriched company data from multiple models to the database.
    
    This function saves one row per model in the database, reusing the created_at
    timestamp from the base company if provided. It first verifies the original
    company exists before proceeding with saves.
    
    Args:
        base_company (dict): Basic company information containing:
            - deal_id: Deal identifier
            - company_id: Company identifier
            - company_name: Name of the company
            - website: Company website URL
            - created_at: Optional creation timestamp
        model_profiles (dict): Dictionary of profiles from different models, where each
            profile contains:
            - profile: Dictionary of company profile data
            - cost_usd: Optional cost in USD
            - tokens: Optional token usage information
    """
    try:
        logger.info(f"Saving enriched company: {base_company['company_name']}")

        with get_db_connection() as conn:
            with conn.begin():
                # Step 1: Verify the original row exists
                check_q = text("""
                    SELECT id FROM da_company_enrichment_data
                     WHERE company_name = :company_name
                     ORDER BY id ASC LIMIT 1
                """)
                res = conn.execute(check_q, {"company_name": base_company["company_name"]})
                if not res.fetchone():
                    logger.error(f"No original row found for {base_company['company_name']}. Aborting save.")
                    return

                # Step 2: Insert one new row per model in the same transaction
                for model_name, model_data in model_profiles.items():
                    try:
                        profile = model_data["profile"]
                        cost_usd = model_data.get("cost_usd", 0)
                        llm_usage = model_data.get("tokens", {})

                        params = {
                            "deal_id": base_company["deal_id"],
                            "company_id": base_company["company_id"],
                            "company_name": base_company["company_name"],
                            "found_company_name": profile.get("found_company_name"),
                            "description": profile.get("description"),
                            "short_description": profile.get("short_description"),
                            "products_services": profile.get("products_services"),
                            "products_services_categories": profile.get("products_services_categories"),
                            "customers_industries": profile.get("customers_industries"),
                            "categories": profile.get("categories"),
                            "industry": profile.get("industry"),
                            "subsectors": profile.get("subsectors"),
                            "business_model": profile.get("business_model"),
                            "website": base_company["website"],
                            "country": profile.get("headquarters", {}).get("country"),
                            "zipcode": profile.get("headquarters", {}).get("zipcode"),
                            "street_address": profile.get("headquarters", {}).get("street_address"),
                            "city": profile.get("headquarters", {}).get("city"),
                            "register_id": profile.get("register_id"),
                            "register_court": profile.get("register_court"),
                            "vat_id": profile.get("vat_id"),
                            "key_managers": safe_json_dumps(profile.get("key_managers")),
                            "linkedin": profile.get("linkedin"),
                            "geographic_markets": profile.get("geographic_markets"),
                            "llm": model_name,
                            "model": model_name,
                            "llm_cost": cost_usd,
                            "llm_usage": safe_json_dumps(llm_usage),
                            "created_at": base_company.get("created_at") or datetime.utcnow(),
                            "updated_at": datetime.utcnow()
                        }

                        insert_q = text("""
                            INSERT INTO da_company_enrichment_data (
                                deal_id, company_id, company_name, found_company_name,
                                description, short_description, products_services,
                                products_services_categories, customers_industries,
                                categories, industry, subsectors, business_model,
                                website, country, zipcode, street_address, city,
                                register_id, register_court, vat_id, key_managers,
                                linkedin, geographic_markets, llm, model, llm_cost,
                                llm_usage, created_at, updated_at
                            )
                            VALUES (
                                :deal_id, :company_id, :company_name, :found_company_name,
                                :description, :short_description, :products_services,
                                :products_services_categories, :customers_industries,
                                :categories, :industry, :subsectors, :business_model,
                                :website, :country, :zipcode, :street_address, :city,
                                :register_id, :register_court, :vat_id, :key_managers,
                                :linkedin, :geographic_markets, :llm, :model, :llm_cost,
                                :llm_usage, :created_at, :updated_at
                            )
                        """)
                        conn.execute(insert_q, params)
                        logger.info(f"Successfully saved profile for model {model_name}")

                    except Exception as e:
                        logger.error(f"Error saving profile for model {model_name}: {e}")
                        raise

    except Exception as e:
        logger.error(f"Failed to save enriched company {base_company['company_name']}: {e}")
        raise
