"""
Token counting utilities for LLM operations.
This module provides functionality for counting tokens in text and dictionaries,
using the tiktoken library with GPT-4o-mini encoding.
"""

import tiktoken

ENCODING = tiktoken.encoding_for_model("gpt-4o-mini")

def count_tokens(text: str) -> int:
    """
    Count the number of tokens in a text string.
    
    Args:
        text (str): Text to count tokens in
        
    Returns:
        int: Number of tokens in the text
    """
    return len(ENCODING.encode(text))

def count_tokens_in_dict(data: dict) -> int:
    """
    Count the total number of tokens in all string values of a dictionary.
    
    Args:
        data (dict): Dictionary containing string values
        
    Returns:
        int: Total number of tokens across all string values
    """
    total = 0
    for content in data.values():
        if isinstance(content, str):
            total += count_tokens(content)
    return total
