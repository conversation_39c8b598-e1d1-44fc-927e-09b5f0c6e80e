# data_enrichment_api/utils/db.py
"""
Database utility functions for the company enrichment service.
This module provides database connection and query functionality for storing and retrieving
company enrichment data. It includes connection management with retry logic and various
query functions for company data.
"""


from sqlalchemy import create_engine, text
import pandas as pd
from data_enrichment_api.config import settings
import time
import logging
from sqlalchemy.exc import OperationalError
from typing import Optional, Dict

# Configure logger
logger = logging.getLogger('db_utils')

# Add connection timeout and retry settings
CONNECT_TIMEOUT = 60 # 60 seconds
READ_TIMEOUT = 60
WRITE_TIMEOUT = 60
MAX_RETRIES = 5      # 5 attempts
RETRY_DELAY = 10     # 10 seconds

# Create engine with connection timeout settings
engine = create_engine(
    url=settings.DATABASE_URL,
    connect_args={
        "connect_timeout": CONNECT_TIMEOUT,
        "read_timeout": READ_TIMEOUT,   # Increased to 60 seconds
        "write_timeout": WRITE_TIMEOUT, # Increased to 60 seconds
    },
    pool_pre_ping=True,        # Enable connection health checks
    pool_recycle=1800,         # Recycle connections after 30 minutes
    pool_timeout=60,           # Wait up to 60 seconds for a connection
    pool_size=20,              # Maximum number of connections in the pool
    max_overflow=10,           # Maximum number of connections that can be created beyond pool_size
    echo=False                 # Disable SQL echo in production
)

def get_db_connection():
    """
    Get a database connection with retry logic.
    
    Returns:
        Connection: A SQLAlchemy database connection
        
    Raises:
        OperationalError: If connection fails after all retry attempts
    """
    for attempt in range(MAX_RETRIES):
        try:
            logger.info(f"Attempting database connection (attempt {attempt + 1}/{MAX_RETRIES})")
            conn = engine.connect()
            logger.info("Successfully connected to database")
            return conn
        except OperationalError as e:
            if attempt == MAX_RETRIES - 1:  # Last attempt
                error_msg = f"Failed to connect to database after {MAX_RETRIES} attempts: {str(e)}"
                logger.error(error_msg)
                raise OperationalError(error_msg) from e
            logger.warning(f"Database connection attempt {attempt + 1} failed: {str(e)}")
            logger.info(f"Waiting {RETRY_DELAY} seconds before next attempt...")
            time.sleep(RETRY_DELAY)
    return None

def fetch_companies_to_validate(start_id=settings.AUTO_ENRICH_START_ID, limit=settings.AUTO_ENRICH_LIMIT):
    """
    Fetch companies that need validation from the database.
    
    Args:
        start_id (int): The ID to start fetching from
        limit (int): Maximum number of companies to fetch
        
    Returns:
        List[Dict]: List of company dictionaries with id, deal_id, company_id, etc.
    """
    query = text("""
        SELECT id, deal_id, company_id, company_name, website, created_at
        FROM da_company_enrichment_data
        WHERE id >= :start_id
        LIMIT :limit
    """)
    with get_db_connection() as conn:
        result = conn.execute(query, {"start_id": start_id, "limit": limit})
        rows = result.fetchall()
        return [{
            "id": row[0],
            "deal_id": row[1],
            "company_id": row[2],
            "company_name": row[3],
            "website": row[4],
            "created_at": row[5]
        } for row in rows]

def get_existing_llm_models(company_name: str) -> set:
    """
    Get the set of LLM models that have already processed this company.
    
    Args:
        company_name (str): Name of the company to check
        
    Returns:
        set: Set of model names that have processed this company
    """
    query = text("""
        SELECT DISTINCT llm
        FROM da_company_enrichment_data
        WHERE company_name = :company_name
    """)
    with get_db_connection() as conn:
        result = conn.execute(query, {"company_name": company_name})
        return {row[0] for row in result.fetchall()}

def get_company_by_id(company_id: int) -> Optional[Dict]:
    """
    Fetch a single company by its ID from the database.
    
    Args:
        company_id (int): The ID of the company to fetch
        
    Returns:
        Optional[Dict]: Company data if found, None otherwise
    """
    try:
        with get_db_connection() as conn:
            # First, let's check the data type and see the actual data
            result = conn.execute(text("""
                SELECT 
                    deal_id,
                    company_id,
                    company_name,
                    website,
                    created_at,
                    CAST(company_id AS CHAR) as company_id_str
                FROM da_company_enrichment_data
                WHERE CAST(company_id AS CHAR) = :company_id
            """), {"company_id": str(company_id)})
            
            row = result.fetchone()
            if row:
                logger.info(f"Found company with ID: {row[1]} (type: {type(row[1])})")
                return {
                    "deal_id": row[0],
                    "company_id": row[1],
                    "company_name": row[2],
                    "website": row[3],
                    "created_at": row[4]
                }
            else:
                # Let's see what IDs we actually have
                check_result = conn.execute(text("""
                    SELECT company_id, CAST(company_id AS CHAR) as company_id_str
                    FROM da_company_enrichment_data
                    ORDER BY company_id
                    LIMIT 5
                """))
                logger.info("Available company IDs:")
                for r in check_result:
                    logger.info(f"ID: {r[0]} (type: {type(r[0])})")
            return None
    except Exception as e:
        logger.error(f"Error fetching company by ID: {e}")
        return None

def get_company_by_deal_id(deal_id: str) -> Optional[Dict]:
    """
    Fetch a single company by its deal_id from the database.
    
    Args:
        deal_id (str): The deal_id of the company to fetch
        
    Returns:
        Optional[Dict]: Company data if found, None otherwise
    """
    try:
        with get_db_connection() as conn:
            result = conn.execute(text("""
                SELECT 
                    deal_id,
                    company_id,
                    company_name,
                    website,
                    created_at
                FROM da_company_enrichment_data
                WHERE deal_id = :deal_id
            """), {"deal_id": deal_id})
            
            row = result.fetchone()
            if row:
                return {
                    "deal_id": row[0],
                    "company_id": row[1],
                    "company_name": row[2],
                    "website": row[3],
                    "created_at": row[4]
                }
            return None
    except Exception as e:
        logging.error(f"Error fetching company by deal_id: {e}")
        return None

def is_deal_id_processed(deal_id: str) -> bool:
    """
    Check if a deal_id has already been processed by checking if it has any LLM model results.
    
    Args:
        deal_id (str): The deal_id to check
        
    Returns:
        bool: True if the deal_id has been processed, False otherwise
    """
    try:
        with get_db_connection() as conn:
            query = text("""
                SELECT COUNT(*) 
                FROM da_company_enrichment_data 
                WHERE deal_id = :deal_id 
                AND llm IS NOT NULL
            """)
            result = conn.execute(query, {"deal_id": deal_id})
            count = result.scalar()
            return count > 0
    except Exception as e:
        logger.error(f"Error checking if deal_id {deal_id} is processed: {e}")
        return False

async def get_unprocessed_companies():
    """
    Fetch all unprocessed companies from the database.
    
    Returns:
        list: List of dictionaries containing deal_id and company information
    """
    try:
        query = text("""
            SELECT DISTINCT d.deal_id, d.company_name, d.website
            FROM da_company_enrichment_data d
            WHERE d.deal_id IS NOT NULL
            AND NOT EXISTS (
                SELECT 1 
                FROM da_company_enrichment_data e 
                WHERE e.deal_id = d.deal_id 
                AND e.llm IS NOT NULL
            )
        """)
        
        with get_db_connection() as conn:
            result = conn.execute(query)
            rows = result.fetchall()
            return [{"deal_id": row[0], "company_name": row[1], "website": row[2]} for row in rows]
    except Exception as e:
        logger.error(f"Error fetching unprocessed companies: {e}")
        raise

