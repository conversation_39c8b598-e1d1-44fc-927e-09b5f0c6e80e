# data_enrichment_api/utils/file_store.py
"""
File storage utilities for company enrichment data.
This module provides functionality for saving and managing company data files,
both locally and in Google Cloud Storage.
"""
import os
import json
import logging
from datetime import datetime
from data_enrichment_api.utils.gcs_service import GCSService

logger = logging.getLogger(__name__)
gcs_service = GCSService()

def save_page_content(base_path: str, slug: str, markdown: str, metadata: dict):
    """
    Save a single page's content to a markdown file and metadata JSON.
    
    Args:
        base_path (str): Base directory path
        slug (str): URL slug for the page
        markdown (str): Markdown content
        metadata (dict): Page metadata
    """
    # Upload markdown to GCS with crawled_pages subdirectory
    gcs_path = f"enriched_companies/{os.path.relpath(base_path, 'outputs')}/crawled_pages/{slug}.md"
    blob = gcs_service.bucket.blob(gcs_path)
    blob.upload_from_string(markdown, content_type="text/markdown")
    
    # Create combined metadata with markdown content
    combined_metadata = {
        **metadata,
        "markdown": markdown,
        "cached_at": datetime.now().isoformat()
    }
    
    # Upload combined metadata to GCS with crawled_pages subdirectory
    meta_gcs_path = f"enriched_companies/{os.path.relpath(base_path, 'outputs')}/crawled_pages/{slug}_meta.json"
    meta_blob = gcs_service.bucket.blob(meta_gcs_path)
    meta_blob.upload_from_string(
        json.dumps(combined_metadata, indent=2, ensure_ascii=False),
        content_type="application/json"
    )

def save_cached_pages(base_path: str, cached_pages: dict):
    """
    Save all cached pages to a single JSON file in the crawled_pages directory.
    
    Args:
        base_path (str): Base directory path
        cached_pages (dict): Dictionary of cached pages
    """
    # Create a combined metadata for all pages
    combined_metadata = {
        "pages": {
            url: {
                "content": content,
                "cached_at": datetime.now().isoformat()
            }
            for url, content in cached_pages.items()
        },
        "total_pages": len(cached_pages),
        "cached_at": datetime.now().isoformat()
    }
    
    # Upload combined metadata to GCS with crawled_pages subdirectory
    gcs_path = f"enriched_companies/{os.path.relpath(base_path, 'outputs')}/crawled_pages/cached_pages.json"
    blob = gcs_service.bucket.blob(gcs_path)
    blob.upload_from_string(
        json.dumps(combined_metadata, indent=2, ensure_ascii=False),
        content_type="application/json"
    )
    
    # Also save individual pages
    for url, content in cached_pages.items():
        slug = url.strip("/") or "index"
        metadata = {
            "url": url,
            "cached_at": datetime.now().isoformat()
        }
        save_page_content(base_path, slug, content, metadata)

async def mirror_to_gcs(local_path: str, company_name: str):
    """
    Mirror a local directory structure to Google Cloud Storage.
    
    Args:
        local_path (str): Local directory path to mirror
        company_name (str): Name of the company being processed
        
    Raises:
        Exception: If mirroring fails
    """
    try:
        logger.info(f"Preparing to mirror directory structure to GCS for company: {company_name}")
        
        # Get the relative path after 'outputs/'
        relative_path = os.path.relpath(local_path, "outputs")
        
        # Walk through the directory
        for root, dirs, files in os.walk(local_path):
            for file in files:
                # Get the local file path
                local_file_path = os.path.join(root, file)
                
                # Get the relative path for GCS
                gcs_relative_path = os.path.relpath(local_file_path, "outputs")
                gcs_path = f"enriched_companies/{gcs_relative_path}"
                
                logger.info(f"Uploading {file} to GCS at {gcs_path}")
                
                # Read the file content
                with open(local_file_path, "r", encoding="utf-8") as f:
                    content = f.read()
                
                # Upload to GCS
                blob = gcs_service.bucket.blob(gcs_path)
                blob.upload_from_string(
                    content,
                    content_type="text/plain" if file.endswith(".md") else "application/json"
                )
                logger.info(f"Successfully uploaded {file} to GCS")
        
        logger.info(f"Successfully mirrored directory structure to GCS for {company_name}")
    except Exception as e:
        logger.error(f"Error mirroring to GCS: {str(e)}")
        raise

async def save_enriched_output(output_base_path: str, raw_data: dict, cleaned_data: dict, profile_data: dict, metadata: dict):
    """
    Save enriched company data to local files and optionally mirror to GCS.
    
    Args:
        output_base_path (str): Base directory path for saving files
        raw_data (dict): Raw crawled content
        cleaned_data (dict): Cleaned and processed content
        profile_data (dict): Generated company profile
        metadata (dict): Additional metadata about the enrichment process
        
    Raises:
        Exception: If saving fails
    """
    try:
        # Commented out local file operations
        # # Save raw markdown
        # raw_path = os.path.join(output_base_path, "_RAW.md")
        # with open(raw_path, "w", encoding="utf-8") as f:
        #     for name, content in raw_data.items():
        #         f.write(f"# {name}\n\n{content}\n\n---\n\n")

        # # Save cleaned markdown
        # cleaned_path = os.path.join(output_base_path, "_CLEANED.md")
        # with open(cleaned_path, "w", encoding="utf-8") as f:
        #     for name, content in cleaned_data.items():
        #         f.write(f"# {name}\n\n{content}\n\n---\n\n")

        # # Save company profile
        # profile_path = os.path.join(output_base_path, "company_profile.json")
        # with open(profile_path, "w", encoding="utf-8") as f:
        #     json.dump(profile_data, f, indent=2, ensure_ascii=False)

        # # Save metadata
        # metadata_path = os.path.join(output_base_path, "metadata.json")
        # with open(metadata_path, "w", encoding="utf-8") as f:
        #     json.dump(metadata, f, indent=2, ensure_ascii=False)

        # Upload directly to GCS
        if "company_name" in metadata:
            company_name = metadata["company_name"]
            gcs_base_path = f"enriched_companies/{os.path.relpath(output_base_path, 'outputs')}"
            
            # Upload raw markdown
            raw_content = "\n\n".join([f"# {name}\n\n{content}\n\n---\n\n" for name, content in raw_data.items()])
            raw_blob = gcs_service.bucket.blob(f"{gcs_base_path}/_RAW.md")
            raw_blob.upload_from_string(raw_content, content_type="text/markdown")
            
            # Upload cleaned markdown
            cleaned_content = "\n\n".join([f"# {name}\n\n{content}\n\n---\n\n" for name, content in cleaned_data.items()])
            cleaned_blob = gcs_service.bucket.blob(f"{gcs_base_path}/_CLEANED.md")
            cleaned_blob.upload_from_string(cleaned_content, content_type="text/markdown")
            
            # Upload company profile
            profile_blob = gcs_service.bucket.blob(f"{gcs_base_path}/company_profile.json")
            profile_blob.upload_from_string(
                json.dumps(profile_data, indent=2, ensure_ascii=False),
                content_type="application/json"
            )
            
            # Upload metadata
            metadata_blob = gcs_service.bucket.blob(f"{gcs_base_path}/metadata.json")
            metadata_blob.upload_from_string(
                json.dumps(metadata, indent=2, ensure_ascii=False),
                content_type="application/json"
            )
            
            logger.info(f"Successfully uploaded all files to GCS for {company_name}")
        else:
            logger.warning("No company_name found in metadata, skipping GCS upload")

    except Exception as e:
        logger.error(f"Error in save_enriched_output: {str(e)}")
        raise

