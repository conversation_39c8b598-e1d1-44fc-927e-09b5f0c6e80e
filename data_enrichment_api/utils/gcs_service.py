"""
Google Cloud Storage service for company enrichment data.
This module provides functionality for storing and retrieving company data in Google Cloud Storage,
including crawled content, company profiles, and metadata.
"""

from google.cloud import storage
import os
from datetime import datetime
import json
from typing import Optional, Dict, Any
from dotenv import load_dotenv
import logging

# Ensure logs directory exists
# os.makedirs('logs', exist_ok=True)

# Configure logging
# logging.basicConfig(
#     level=logging.INFO,
#     format='%(asctime)s - %(levelname)s - %(message)s',
#     handlers=[
#         logging.FileHandler('logs/gcs_operations.log'),
#         logging.StreamHandler()
#     ]
# )
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class GCSService:
    """
    Service for interacting with Google Cloud Storage.
    
    This class manages the storage and retrieval of company enrichment data in GCS,
    including crawled content, company profiles, and metadata.
    """
    def __init__(self):
        """
        Initialize GCS service with bucket configuration.
        
        Raises:
            ValueError: If GCS configuration is missing
            Exception: If GCS client initialization fails
        """
        self.project_id = os.getenv("PROJECT_ID")
        self.bucket_name = os.getenv("GCS_OUTPUT_BUCKET_NAME")
        self.enriched_companies_dir = "enriched_companies"
        
        if not self.project_id or not self.bucket_name:
            error_msg = "GCS configuration missing. Please set PROJECT_ID and GCS_OUTPUT_BUCKET_NAME in .env"
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        logger.info(f"Initializing GCS service with project_id: {self.project_id}, bucket: {self.bucket_name}")
        
        try:
            self.client = storage.Client(project=self.project_id)
            self.bucket = self.client.bucket(self.bucket_name)
            logger.info(f"Successfully connected to GCS bucket: {self.bucket_name}")
        except Exception as e:
            logger.error(f"Failed to initialize GCS client: {e}")
            raise
        
        # Ensure the enriched_companies directory exists
        self._ensure_directory_exists()

    def _ensure_directory_exists(self):
        """
        Ensure the enriched_companies directory exists in the bucket.
        
        Raises:
            Exception: If directory creation fails
        """
        try:
            blob = self.bucket.blob(f"{self.enriched_companies_dir}/")
            if not blob.exists():
                logger.info(f"Creating enriched_companies directory in bucket {self.bucket_name}")
                blob.upload_from_string("")
                logger.info("Directory created successfully")
            else:
                logger.info("enriched_companies directory already exists")
        except Exception as e:
            logger.error(f"Failed to ensure directory exists: {e}")
            raise

    def _get_blob_path(self, company_name: str, filename: str) -> str:
        """
        Generate the blob path for a company's file.
        
        Args:
            company_name (str): Name of the company
            filename (str): Name of the file
            
        Returns:
            str: Full path for the blob in GCS
        """
        path = f"{self.enriched_companies_dir}/{filename}"
        logger.debug(f"Generated blob path: {path}")
        return path

    async def upload_crawled_content(self, company_name: str, content_data: dict) -> None:
        """
        Upload crawled content to GCS.
        
        Args:
            company_name (str): Name of the company
            content_data (dict): Content data to upload
            
        Raises:
            Exception: If upload fails
        """
        try:
            logger.info(f"Preparing to upload content for company: {company_name}")
            # Create a unique filename based on timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{timestamp}.json"
            # Create company-specific directory path
            company_dir = f"enriched_companies/{company_name}"
            blob_path = f"{company_dir}/{filename}"
            
            logger.info(f"Creating blob at path: {blob_path}")
            blob = self.bucket.blob(blob_path)
            
            # Convert content to JSON string
            logger.info(f"Converting content to JSON for {filename}")
            content_json = json.dumps(content_data, ensure_ascii=False)
            
            # Upload the content
            logger.info(f"Uploading content to {blob_path}")
            blob.upload_from_string(
                content_json,
                content_type="application/json"
            )
            logger.info(f"Successfully uploaded content to {blob_path}")
            
        except Exception as e:
            logger.error(f"Error uploading content to GCS: {str(e)}")
            raise

    async def upload_company_profile(self, company_name: str, profile_data: Dict[str, Any]) -> bool:
        """
        Upload company profile to GCS.
        
        Args:
            company_name (str): Name of the company
            profile_data (Dict[str, Any]): Profile data to upload
            
        Returns:
            bool: True if upload succeeds, False otherwise
        """
        try:
            logger.info(f"Preparing to upload profile for company: {company_name}")
            # Create a unique filename based on timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"profile_{timestamp}.json"
            # Create company-specific directory path
            company_dir = f"enriched_companies/{company_name}"
            blob_path = f"{company_dir}/{filename}"
            
            logger.info(f"Creating blob at path: {blob_path}")
            blob = self.bucket.blob(blob_path)
            
            # Convert profile to JSON string
            logger.info(f"Converting profile to JSON for {filename}")
            profile_json = json.dumps(profile_data, ensure_ascii=False)
            
            # Upload the profile
            logger.info(f"Uploading profile to {blob_path}")
            blob.upload_from_string(
                profile_json,
                content_type="application/json"
            )
            logger.info(f"Successfully uploaded profile to {blob_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error uploading profile to GCS: {str(e)}")
            return False

    async def get_latest_company_data(self, company_name: str) -> Optional[Dict[str, Any]]:
        """
        Get the latest company data from GCS.
        
        Args:
            company_name (str): Name of the company
            
        Returns:
            Optional[Dict[str, Any]]: Latest company data if found, None otherwise
        """
        try:
            logger.info(f"Looking for latest data for company: {company_name}")
            # List all blobs in the company's directory
            company_dir = f"enriched_companies/{company_name}"
            blobs = self.bucket.list_blobs(prefix=company_dir)
            
            # Get the most recent blob
            latest_blob = None
            latest_time = None
            
            for blob in blobs:
                if blob.name.endswith('.json'):
                    if latest_blob is None or blob.time_created > latest_time:
                        latest_blob = blob
                        latest_time = blob.time_created
            
            if latest_blob:
                logger.info(f"Found latest data for {company_name} at {latest_blob.name}")
                content = latest_blob.download_as_text()
                return json.loads(content)
            else:
                logger.warning(f"No data found for company: {company_name}")
                return None
                
        except Exception as e:
            logger.error(f"Error retrieving company data from GCS: {str(e)}")
            return None

# Create a singleton instance
gcs_service = GCSService()