# data_enrichment_api/main_multi.py
"""
Main entry point for the multi-model company enrichment service.
This module provides the FastAPI application setup and route configuration
for the company enrichment service that supports multiple LLM models.
"""

import logging, sys
from fastapi import FastAPI
from google.cloud import logging as cloud_logging

from data_enrichment_api.services.validator.api import router as validator_router
from data_enrichment_api.services.crawler.api import router as crawler_router
from data_enrichment_api.services.content_cleaner.api import router as cleaner_router
from data_enrichment_api.services.company_profiler.api_multi import router as profiler_multi_router 
# from data_enrichment_api.orchestrator_multi import router as orchestrator_router
from data_enrichment_api.orchestrator_parallel import router as parallel_router

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)]
)
client = cloud_logging.Client()
client.setup_logging(log_level=logging.INFO)

app = FastAPI(
    title="Data Enrichment API (Multi-Model)",
    description="API for enriching company data using multiple LLM models",
    version="1.0.0"
)

# Include routers
app.include_router(validator_router, prefix="/validator", tags=["Validator"])
app.include_router(crawler_router, prefix="/crawler", tags=["Crawler"])
app.include_router(cleaner_router, prefix="/cleaner", tags=["Content Cleaner"])
app.include_router(profiler_multi_router, prefix="/profiler-multi", tags=["Company Profiler (Multi-Model)"])
# app.include_router(orchestrator_router, prefix="/orchestrator", tags=["Orchestrator (Multi-Model)"])
app.include_router(parallel_router, prefix="/orchestrator-parallel", tags=["Orchestrator (Parallel)"])

@app.get("/")
async def root():
    """
    Root endpoint for health checks.
    
    Returns:
        dict: Simple status message
    """
    return {"status": "ok", "message": "Company Enrichment API is running"}