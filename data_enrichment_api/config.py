# data_enrichment_api/config.py
"""
Configuration settings for the company enrichment service.
This module provides centralized configuration management using Pydantic settings,
including database connections, API endpoints, and service-specific settings.
"""

from pydantic_settings import BaseSettings, SettingsConfigDict

class Settings(BaseSettings):
    """
    Application settings class.

    This class manages all configuration settings for the company enrichment service,
    including database connections, API endpoints, and service-specific settings.
    Settings are loaded from environment variables with sensible defaults.
    """
    model_config = SettingsConfigDict(env_file=".env", env_file_encoding="utf-8", extra="allow")

    # Database configuration
    DATABASE_URL: str

    # API Keys
    openai_api_key: str
    anthropic_api_key: str

    # Crawler settings
    max_depth: int = 1
    page_timeout_ms: int = 30000
    crawl_delay_seconds: float = 0.3
    max_internal_links: int = 20  # Maximum number of internal links to crawl

    # Google Cloud Storage settings
    project_id: str
    gcs_output_bucket_name: str
    gcs_input_bucket_name: str

    # Auto-enrichment settings
    AUTO_ENRICH_START_ID: int = 1
    AUTO_ENRICH_LIMIT: int = 1

#     class Config:
#         env_file = ".env"
#         env_file_encoding = "utf-8"

settings = Settings()
