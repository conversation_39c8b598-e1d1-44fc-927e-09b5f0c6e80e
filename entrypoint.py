import asyncio, logging, json
from crawl4ai import CrawlResult

from company_entrichment_service.config import WORKDIR
# import company_entrichment_service.infrastructure.crawler as crawler
from company_entrichment_service.infrastructure.crawler import crawler, _crawler_run_config

logger = logging.getLogger('__job__')

website = 'http://grasmehr.de'
# website = 'http://fake-website.host'

def handle_result(result: CrawlResult):
    logger.info(f'result.success: {result.success}')
    logger.info(f'result.error_message: {result.error_message}')
    logger.info(f'result.url: {result.url}')
    logger.info(f'result.status_code: {result.status_code}')

    # print("Original HTML size:", len(result.html))
    # print("Cleaned HTML size:", len(result.cleaned_html or ""))

    # Markdown output
    # if result.markdown:
    #     print("Raw Markdown:", result.markdown.raw_markdown[:300])
    #     print("Citations Markdown:", result.markdown.markdown_with_citations[:300])
    #     if result.markdown.fit_markdown:
    #         print("Fit Markdown:", result.markdown.fit_markdown[:200])

    # Media & Links
    # if "images" in result.media:
    #     print("Image count:", len(result.media["images"]))
    # if "internal" in result.links:
    #     print("Internal link count:", len(result.links["internal"]))

    logger.info(f'result.extracted_content: {result.extracted_content}')
    logger.info(f'result.downloaded_files: {result.downloaded_files}')

    # Screenshot/PDF/MHTML
    # if result.screenshot:
    #     print("Screenshot length:", len(result.screenshot))
    # if result.pdf:
    #     print("PDF bytes length:", len(result.pdf))
    # if result.mhtml:
    #     print("MHTML length:", len(result.mhtml))

    # Network and console capturing
    # if result.network_requests:
    #     print(f"Network requests captured: {len(result.network_requests)}")
    #     # Analyze request types
    #     req_types = {}
    #     for req in result.network_requests:
    #         if "resource_type" in req:
    #             req_types[req["resource_type"]] = req_types.get(req["resource_type"], 0) + 1
    #     print(f"Resource types: {req_types}")

    # if result.console_messages:
    #     print(f"Console messages captured: {len(result.console_messages)}")
    #     # Count by message type
    #     msg_types = {}
    #     for msg in result.console_messages:
    #         msg_types[msg.get("type", "unknown")] = msg_types.get(msg.get("type", "unknown"), 0) + 1
    #     print(f"Message types: {msg_types}")

async def main():
    logger.info('main function...')
    logger.info(f'WORKDIR: {WORKDIR}')

    logger.info(f'Crawling website: {website}')
    result = await crawler.crawl(website)
    logger.info(f'Crawled website: {website}')
    logger.info(f'len(result): {len(result)}')

    for r in result:
        logger.info(f'{r.url}: {r.markdown}')

    return

    # response = await get_unprocessed_companies_endpoint()
    # logger.info("Companies list fetched from GCS successfully")
    #
    # logger.info("Processing companies list...")
    # await enrich_parallel(
    #     ParallelEnrichRequest(companies=response['companies'])
    # )
    # logger.info("Done processing companies list...")

async def main_crawl():
    await crawler.start()
    try:
        result = await crawler.arun(url=website, config=_crawler_run_config)
        for r in result:
            print(f'{r.url}: {r.markdown}')
    finally:
        await crawler.close()

    return

if __name__ == "__main__":
    # asyncio.run(main())
    asyncio.run(main_crawl())

# async def tmp():
#     websites = ["", "", "" ] # ...
#
#     for w in websites:
#         await crawler.crawl(w)
