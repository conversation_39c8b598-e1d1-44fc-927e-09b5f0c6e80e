import logging, sys, asyncio, psutil
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig, CacheMode, BFSDeepCrawlStrategy, BrowserConfig, LXMLWebScrapingStrategy

from urls import companies

logging.basicConfig(
    level=logging.INFO,
    # format="%(asctime)s %(levelname)s %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

async def throttle(threshold_percent: float = 80.0, interval: float = 0.5):
    logger.info(f"Memory usage: {psutil.virtual_memory().percent}%")

    while psutil.virtual_memory().percent >= threshold_percent:
        await asyncio.sleep(interval)

max_parallel = 5
async def main_multi_but_it_is_arun():
    browser_config = BrowserConfig(headless=True, verbose=True)
    scraping_strategy = LXMLWebScrapingStrategy()
    deep_crawl_strategy = BFSDeepCrawlStrategy(
        max_depth=1,  # Crawl initial page + 1 level deep
        include_external=False,  # Stay within the same domain
    )
    config = CrawlerRunConfig(
        cache_mode=CacheMode.BYPASS,
        page_timeout=30000,  # 30-second timeout per company
        scraping_strategy=scraping_strategy,
        deep_crawl_strategy=deep_crawl_strategy,
        verbose=False,
    )

    semaphore = asyncio.Semaphore(max_parallel)

    urls = [company["website"] for company in companies]
    results = []

    async with AsyncWebCrawler(config=browser_config, verbose=True) as crawler:
        logger.info(f"Starting crawling for {len(urls)} companies...")

        # @todo: extract this as a standalone function, should accept the crawler as an argument
        async def crawl_one(url: str):
            async with semaphore:
                await throttle()
                result = await crawler.arun(url=url, config=config)
                logger.info(f"Result.success: {result[0].success}, website: {url}, error: {result[0].error_message}")
                # logger.info(f"Result: {result[0]}")

                results.append({"url": url, "success": result[0].success, "error": result[0].error_message})

                # processing
                # write the result to DB
                # write results to GCS

                return result

        await asyncio.gather(*(crawl_one(url) for url in urls))

        logger.info(f"Results {results}")
        logger.info(f"Done crawling for {len(urls)} companies...")

if __name__ == "__main__":
    asyncio.run(main_multi_but_it_is_arun())
