FROM python:3.11-slim

# Install system dependencies for Play<PERSON> (required for Chromium, etc.)
RUN apt-get update && apt-get install -y \
    wget gnupg ca-certificates curl unzip fonts-liberation \
    libnss3 libatk-bridge2.0-0 libxss1 libasound2 libxcomposite1 libxrandr2 libxdamage1 \
    libgbm1 libgtk-3-0 libdrm2 libx11-xcb1 libxshmfence1 libxext6 \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

WORKDIR /app

COPY . .

RUN pip install --no-cache-dir -r requirements.txt && pip install playwright && playwright install --with-deps chromium

ENV PYTHONUNBUFFERED=1

# CMD cat /etc/secrets/env > .env && uvicorn data_enrichment_api.main_multi:app --host 0.0.0.0 --port 8080
CMD python main.py
