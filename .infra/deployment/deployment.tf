resource "google_service_account" "targets-enrichment-c-run-admin" {
  project                      = local.project
  account_id                   = "targets-enrichment-c-run-admin"
  display_name                 = "Cloud Run Admin Service Account for targets-enrichment"
  create_ignore_already_exists = true
}

resource "google_project_iam_member" "cloud_run_admin_role" {
  project = local.project
  role    = "roles/run.admin"
  member  = "serviceAccount:${google_service_account.targets-enrichment-c-run-admin.email}"
}
resource "google_project_iam_member" "secretmanager_secret_accessor_role" {
  project = local.project
  role    = "roles/secretmanager.secretAccessor"
  member  = "serviceAccount:${google_service_account.targets-enrichment-c-run-admin.email}"
}
resource "google_project_iam_member" "storage_admin" {
  project = local.project
  role   = "roles/storage.admin"
  member = "serviceAccount:${google_service_account.targets-enrichment-c-run-admin.email}"
}

# @todo: refactor to v2
# resource "google_cloud_run_service" "targets-enrichment" {
#   name     = "targets-enrichment-${local.name}"
#   location = local.region
#
#   template {
#     spec {
#       containers {
#         image = local.image_service
#
#         resources {
#           limits = {
#             cpu    = "2"
#             memory = "5120Mi"
#           }
#         }
#
#         volume_mounts {
#           name       = "env-volume"
#           mount_path = "/etc/secrets"
#         }
#       }
#
#       volumes {
#         name = "env-volume"
#         secret {
#           secret_name = "targets-enrichment-env"
#           items {
#             key  = "latest"
#             path = "env"
#           }
#         }
#       }
#
#       service_account_name = google_service_account.targets-enrichment-c-run-admin.email
#       timeout_seconds = 1800
#       container_concurrency = 120
#     }
#
#     metadata {
#       annotations = {
#         "autoscaling.knative.dev/maxScale" = "1"
#         "run.googleapis.com/cloudsql-instances" = local.db_instance
#       }
#     }
#   }
#
#   traffic {
#     percent         = 100
#     latest_revision = true
#   }
#
#   autogenerate_revision_name = true
#
#   depends_on = [
#     google_project_iam_member.cloud_run_admin_role,
#     google_project_iam_member.secretmanager_secret_accessor_role
#   ]
# }

# data "google_iam_policy" "noauth" {
#   binding {
#     role = "roles/run.invoker"
#     members = [
#       "allUsers",
#     ]
#   }
# }

# resource "google_cloud_run_service_iam_policy" "noauth" {
#   location = google_cloud_run_service.targets-enrichment.location
#   project  = google_cloud_run_service.targets-enrichment.project
#   service  = google_cloud_run_service.targets-enrichment.name
#
#   policy_data = data.google_iam_policy.noauth.policy_data
# }

resource "google_cloud_run_v2_job" "multi-crawler" {
  name     = local.name
  location = local.region

  template {
    template {
      containers {
        image = local.image_job

        resources {
          limits = {
            cpu    = "2"
            # Amount of memory; must be ≥ 512Mi (and ≥ 1Gi if you want ≥ 0.583 vCPU)
            memory = "5Gi"
          }
        }

        volume_mounts {
          name       = "env-volume"
          mount_path = "/etc/secrets"
        }

        volume_mounts {
          name = "cloudsql"
          mount_path = "/cloudsql"
        }
      }

      volumes {
        name = "env-volume"
        secret {
          secret = "projects/${local.project}/secrets/targets-enrichment-env"
          items {
            path    = "env"
            version = "latest"
          }
        }
      }

      volumes {
        name = "cloudsql"
        cloud_sql_instance {
          instances = [local.db_instance]
        }
      }

      service_account = google_service_account.targets-enrichment-c-run-admin.email

      timeout = "3600s"
      max_retries = 0
    }

    # Optionally configure job execution parameters:
    # parallelism = 1         # number of tasks to run in parallel (for multiple instances)
    # tasks = 1               # total tasks (job instances) to run
  }

  depends_on = [google_service_account.targets-enrichment-c-run-admin]
}
