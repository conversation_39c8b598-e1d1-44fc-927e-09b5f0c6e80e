variable "project" {
  type = string
}

variable "region" {
  type = string
}

variable "name" {
  type = string
}

variable "image_service" {
  type = string
}

variable "image_job" {
  type = string
}

locals {
  project       = var.project
  region        = var.region
  name          = var.name
  image_service = var.image_service
  image_job     = var.image_job
  db_instance   = "dealcircle001:europe-west3:dc-prod"
}
