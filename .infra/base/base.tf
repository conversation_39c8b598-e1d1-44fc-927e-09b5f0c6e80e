# resource "google_artifact_registry_repository" "targets-enrichment-artifact-repo" {
#   location      = local.region
#   repository_id = "targets-enrichment-artifact-repo-${local.name}"
#   description   = "Repository responsible to store docker images for `targets-enrichment` Cloud Run deployments. Environment: ${local.name}"
#   format        = "DOCKER"
#
#   cleanup_policies {
#     id     = "keep-minimum-versions"
#     action = "KEEP"
#
#     most_recent_versions {
#       keep_count = 3
#     }
#   }
# }

resource "google_artifact_registry_repository" "job-artifact-repo" {
  location        = local.region
  repository_id   = "job-artifact-repo-${local.name}"
  description     = "Repository responsible to store docker images for `targets-enrichment` Cloud Run job. Environment: ${local.name}"
  format          = "DOCKER"
}
