variable "DOCKER_IMAGE_SERVICE" {
  description = "Docker image URL for Cloud Run service."
  type        = string
}

variable "DOCKER_IMAGE_JOB" {
  description = "Docker image URL for Cloud Run Job."
  type        = string
}

variable "PROJECT_ID" {
  description = "GCP Project ID."
  type        = string
}

variable "REGION" {
  description = "GCP Region."
  type        = string
}

locals {
  project       = var.PROJECT_ID
  region        = var.REGION
  name          = terraform.workspace
  image_service = var.DOCKER_IMAGE_SERVICE
  image_job     = var.DOCKER_IMAGE_JOB
}
