import logging

from crawl4ai import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CrawlerRunConfig, CacheMode, BFSDeepCrawlStrategy, BrowserConfig
from crawl4ai.markdown_generation_strategy import DefaultMarkdownGenerator

_browser_config = BrowserConfig(headless=True, verbose=True)
_deep_crawl_strategy = BFSDeepCrawlStrategy(
    max_depth=1,  # Crawl initial page + 1 level deep
    include_external=False,  # Stay within the same domain
)
_markdown_generator = DefaultMarkdownGenerator()
_crawler_run_config = CrawlerRunConfig(
    cache_mode=CacheMode.BYPASS,
    page_timeout=30000,  # 30-second timeout per company
    deep_crawl_strategy=_deep_crawl_strategy,
    markdown_generator=_markdown_generator,
    excluded_tags=['script', 'style', 'img', 'form', 'header', 'footer', 'nav'],
    only_text=True,
    remove_forms=True,
    exclude_external_images=True,
    verbose=True,
)

logger = logging.getLogger('__crawler__')

class Crawler:
    _async_crawler: AsyncWebCrawler

    def __init__(self):
        self._async_crawler = AsyncWebCrawler(config=_browser_config, verbose=True)

    @classmethod
    async def create(cls):
        try:
            instance = cls()
            await instance._async_crawler.start()
            return instance
        except Exception as e:
            logger.error(e)
            raise e

    async def crawl(self, url: str):
        result = await self._async_crawler.arun(url=url, config=_crawler_run_config)
        return result
        # markdown = ''.join([r.markdown for r in result])
        # return Crawler._dedupe_markdown_lines(markdown)

    @classmethod
    def _dedupe_markdown_lines(cls, markdown: str) -> str:
        seen = set()
        unique_lines = []

        for line in markdown.splitlines():
            trimmed = line.strip()
            if trimmed and trimmed not in seen:
                seen.add(trimmed)
                unique_lines.append(line)

        return "\n".join(unique_lines)