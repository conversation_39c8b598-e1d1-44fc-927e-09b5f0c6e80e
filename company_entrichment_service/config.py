import os, logging
from dotenv import load_dotenv
from google.cloud import logging as cloud_logging
from google.cloud.logging_v2.handlers import StructuredLogHandler

logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
    handlers=[StructuredLogHandler(level=logging.INFO, client=cloud_logging.Client())]
)

load_dotenv()

WORKDIR = os.getenv('WORKDIR')
if WORKDIR is None:
    raise ValueError('WORKDIR not set')
