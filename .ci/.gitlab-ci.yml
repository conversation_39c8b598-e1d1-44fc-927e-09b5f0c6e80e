workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      variables:
        ENVIRONMENT: $CI_COMMIT_REF_SLUG
        SERVICE_ACCOUNT: $GCP_SERVICEACCOUNT_DEV
        TFBACKEND_GCS_CONFIG: ./config/dev.gcp.tfbackend
        TF_VAR_PROJECT_ID: ai-crawler-459216
        DOCKER_IMAGE_NAME: targets-enrichment-dev
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH && $CI_COMMIT_MESSAGE =~ /^chore\(release\)\:/
      variables:
        ENVIRONMENT: production
        SERVICE_ACCOUNT: $GCP_SERVICEACCOUNT_PROD
        TFBACKEND_GCS_CONFIG: ./config/production.gcp.tfbackend
        TF_VAR_PROJECT_ID: ai-crawler-... # @todo: add proper GCP Project ID later for `prod`
        DOCKER_IMAGE_NAME: targets-enrichment

variables:
  TF_VAR_REGION: europe-west3
  DOCKER_REPO: https://${TF_VAR_REGION}-docker.pkg.dev/${TF_VAR_PROJECT_ID}/targets-enrichment-artifact-repo-${ENVIRONMENT}
  TF_DIR: ${CI_PROJECT_DIR}/.infra
  TF_VAR_DOCKER_IMAGE_SERVICE: ${TF_VAR_REGION}-docker.pkg.dev/${TF_VAR_PROJECT_ID}/targets-enrichment-artifact-repo-${ENVIRONMENT}/${DOCKER_IMAGE_NAME}:${CI_COMMIT_SHORT_SHA}
  TF_VAR_DOCKER_IMAGE_JOB: ${TF_VAR_REGION}-docker.pkg.dev/${TF_VAR_PROJECT_ID}/job-artifact-repo-${ENVIRONMENT}/job:${CI_COMMIT_SHORT_SHA}

stages:
  - terraform-apply-base
  - docker_build_and_push
  - terraform-apply-deployment
  - mr-comment
  - terraform-destroy

terraform-apply-base:
  stage: terraform-apply-base
  image:
    name: hashicorp/terraform:latest
    entrypoint: [""]
  before_script:
    - terraform --version
    - cd ${TF_DIR}
    - mkdir -p ./creds
    - echo $SERVICE_ACCOUNT > ./creds/serviceaccount.json
    - terraform init -reconfigure -backend-config=$TFBACKEND_GCS_CONFIG
    - terraform workspace select -or-create $ENVIRONMENT
  script:
    - terraform validate
    - terraform apply -target=module.base -auto-approve
  environment:
    name: $ENVIRONMENT
    action: start
    on_stop: terraform-destroy

docker_build_and_push:
  stage: docker_build_and_push
  image: docker:latest
  services:
    - docker:dind
  before_script:
    - docker -v
  script:
    - echo $SERVICE_ACCOUNT | docker login -u _json_key --password-stdin $DOCKER_REPO
#    - docker build -t $TF_VAR_DOCKER_IMAGE_SERVICE .
    - docker build -t $TF_VAR_DOCKER_IMAGE_JOB .
#    - docker push $TF_VAR_DOCKER_IMAGE_SERVICE
    - docker push $TF_VAR_DOCKER_IMAGE_JOB
  dependencies:
    - terraform-apply-base
  #  @todo: implement proper docker layer cache here
  #  cache:
  #    key: ${CI_COMMIT_REF_NAME}
  #    paths:
  #      - ${WORK_DIR}/${SOURCE_DIR}
  #    policy: pull-push

terraform-apply-deployment:
  stage: terraform-apply-deployment
  image:
    name: hashicorp/terraform:latest
    entrypoint: [""]
  before_script:
    - terraform --version
    - cd ${TF_DIR}
    - mkdir -p ./creds
    - echo $SERVICE_ACCOUNT > ./creds/serviceaccount.json
    - terraform init -reconfigure -backend-config=$TFBACKEND_GCS_CONFIG
    - terraform workspace select $ENVIRONMENT
  script:
    - terraform validate
    - terraform apply -target=module.deployment -auto-approve
#    - echo "SERVICE_URL=$(terraform output -raw module_deployment_output_service_url)" > ../variables.env
  dependencies:
    - docker_build_and_push
#  artifacts:
#    reports:
#      dotenv: variables.env

terraform-destroy:
  stage: terraform-destroy
  image:
    name: hashicorp/terraform:latest
    entrypoint: [""]
  before_script:
    - terraform --version
    - cd ${TF_DIR}
    - mkdir -p ./creds
    - echo $SERVICE_ACCOUNT > ./creds/serviceaccount.json
    - terraform init -reconfigure -backend-config=$TFBACKEND_GCS_CONFIG
    - terraform workspace select $ENVIRONMENT
  script:
    - terraform destroy -auto-approve
    - terraform workspace select default
    - terraform workspace delete $ENVIRONMENT
  dependencies:
    - terraform-apply-deployment
  environment:
    name: $ENVIRONMENT
    action: stop
  when: manual
  allow_failure: true


# @info: `mr-comment` job is a small helper for developer which is responsible to output successfully deployed
#   Cloud Run service URI directly to GitLab merge request overview tab under the `Activity` section
#mr-comment:
#  stage: mr-comment
#  image: curlimages/curl:latest
#  script:
#    - |
#      # Use the API to post `module_deployment_output_service_url` as a comment to the corresponding MR
#      curl --request POST \
#        --header "PRIVATE-TOKEN: ${GITLAB_PRIVATE_TOKEN}" \
#        --data "body=Deployed to: ${SERVICE_URL}" \
#        "https://gitlab.com/api/v4/projects/${CI_PROJECT_ID}/merge_requests/${CI_MERGE_REQUEST_IID}/notes"
#  dependencies:
#    - terraform-apply-deployment
#  rules:
#    - if: '$ENVIRONMENT != "staging" && $ENVIRONMENT != "production"'