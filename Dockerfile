FROM python:3.11-slim AS base

# Install system dependencies for Play<PERSON> (required for Chromium, etc.)
RUN apt-get update && apt-get install -y \
    wget gnupg ca-certificates curl unzip fonts-liberation \
    libnss3 libatk-bridge2.0-0 libxss1 libasound2 libxcomposite1 libxrandr2 libxdamage1 \
    libgbm1 libgtk-3-0 libdrm2 libx11-xcb1 libxshmfence1 libxext6 \
    && apt-get clean && rm -rf /var/lib/apt/lists/* \
    && pip install playwright && playwright install --with-deps chromium

FROM base AS builder

WORKDIR /app

COPY /requirements.txt ./requirements.txt

RUN pip install --no-cache-dir -r requirements.txt

FROM builder AS runner

# Create a non-root user for security
RUN useradd --create-home --shell /bin/bash app

# Copy application code and set ownership
COPY --chown=app:app . .

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/app

# Switch to non-root user
USER app

# Default command - can be overridden for serverless deployment
# Use exec form to ensure proper signal handling
CMD ["python", "entrypoint.py"]
