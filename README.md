# DealCircle Data Enrichment API

A robust API service for enriching company data through web crawling, content cleaning, and multi-model LLM-based profiling.

## Overview

This service provides a comprehensive pipeline for enriching company data by:
1. Fetching company information from PostgreSQL database
2. Crawling company websites
3. Cleaning and processing crawled content
4. Generating detailed company profiles using multiple LLM models
5. Storing enriched data in Google Cloud Storage (GCS)
6. Saving structured data to a PostgreSQL database

## Repository Structure

```
data_enrichment_api/
├── services/
│   ├── validator/
│   │   ├── api.py          # URL validation API endpoints
│   │   ├── core.py         # URL validation logic
│   │   └── __init__.py
│   ├── crawler/
│   │   ├── api.py          # Crawler API endpoints
│   │   ├── core.py         # Core crawling logic
│   │   └── __init__.py
│   ├── content_cleaner/
│   │   ├── api.py          # Content cleaner API endpoints
│   │   ├── core.py         # Content cleaning logic
│   │   └── __init__.py
│   └── company_profiler/
│       ├── api_multi.py    # Multi-model profiler API endpoints
│       ├── multimodel_runner.py  # Multi-model orchestration
│       ├── prompts.py      # LLM prompts
│       └── __init__.py
├── utils/
│   ├── db.py              # Database connection and queries
│   ├── db_saver.py        # Database operations
│   ├── file_store.py      # File storage operations
│   ├── gcs_service.py     # GCS operations
│   ├── token_utils.py     # Token management
│   └── __init__.py
├── llm_clients/
│   ├── base.py            # Base LLM client
│   ├── gpt41_mini_client.py
│   ├── gpt41_nano_client.py
│   ├── claude3_haiku_client.py
│   ├── claude35_sonnet_client.py
│   ├── claude37_sonnet_client.py
│   └── __init__.py
├── main_multi.py          # FastAPI application entry point
├── orchestrator_multi.py  # Pipeline orchestration
└── config.py             # Configuration management
```

## Workflow

### 1. Data Flow

```mermaid
graph LR
    A[PostgreSQL DB] -->|Fetch Company Info| B[Validator Service]
    B -->|Valid URL| C[Crawler Service]
    C -->|Raw Content| D[Content Cleaner]
    D -->|Cleaned Content| E[Company Profiler]
    E -->|Company Profile| F[GCS Storage]
    E -->|Enriched Data| G[PostgreSQL DB]
    B -->|Invalid URL| H[Error Handler]
```

### 2. Detailed Process

1. **Database Initialization**
   - Service fetches company information from PostgreSQL using deal_id
   - Each company record contains:
     - Deal ID
     - Company ID
     - Company Name
     - Website URL

2. **URL Validation Phase**
   - Validator service checks the company URL
   - Performs the following validations:
     - URL format validation
     - Domain accessibility check
     - Robots.txt compliance
     - SSL certificate verification
   - Updates validation status in database
   - Only proceeds to crawling if URL is valid

3. **Crawling Phase**
   - Crawler service processes the company URL
   - Implements rate limiting and politeness controls
   - Extracts content from the website
   - Stores raw content in GCS under `crawled_pages/{company_name}/`

4. **Content Cleaning**
   - Processes raw crawled content
   - Removes noise and irrelevant content
   - Structures content for LLM processing
   - Stores cleaned content in GCS under `cleaned_content/{company_name}/`

5. **Profile Generation**
   - Multi-model LLM processing
   - Parallel execution for efficiency
   - Generates structured company profile
   - Stores profiles in GCS under `company_profiles/{company_name}/`

6. **Data Storage**
   - GCS Storage Structure:
     ```
     enriched_companies/
     └── company-name/
         └── timestamp/
             ├── crawled_pages/
             │   ├── page1.md
             │   ├── page1_meta.json
             │   └── ...
             ├── _RAW.md
             ├── _CLEANED.md
             ├── company_profile.json
             └── metadata.json
     ```
   - Database Updates:
     - Stores enriched data in `da_company_enrichment_data` table
     - Updates processing status and timestamps

### 3. Database Schema

```sql
CREATE TABLE da_company_enrichment_data (
    id SERIAL PRIMARY KEY,
    deal_id INTEGER NOT NULL,
    company_id INTEGER,
    company_name VARCHAR(255),
    found_company_name VARCHAR(255),
    description TEXT,
    short_description TEXT,
    products_services TEXT,
    products_services_categories TEXT,
    customers_industries TEXT,
    categories TEXT,
    industry VARCHAR(255),
    subsectors TEXT,
    business_model TEXT,
    website VARCHAR(255),
    country VARCHAR(255),
    zipcode VARCHAR(255),
    street_address TEXT,
    city VARCHAR(255),
    register_id VARCHAR(255),
    register_court VARCHAR(255),
    vat_id VARCHAR(255),
    key_managers JSONB,
    linkedin VARCHAR(255),
    geographic_markets TEXT,
    llm VARCHAR(255),
    model VARCHAR(255),
    llm_cost DECIMAL,
    llm_usage JSONB,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

### 4. API Integration

#### Single Company Enrichment
To enrich a single company's data, send a POST request to the orchestrator endpoint:

```bash
curl -X POST "http://localhost:8000/orchestrator/enrich" \
     -H "Content-Type: application/json" \
     -d '{
       "deal_id": "80"
     }'
```

#### Request Format
```json
{
  "deal_id": "string"  // The ID of the deal to process
}
```

#### Response Format
```json
{
  "deal_id": 80,
  "company_id": null,
  "company_name": "J.S. Logistik GmbH",
  "url": "https://jslogistics.eu",
  "saved_models": [
    "claude-3-7-sonnet-20250219"
  ],
  "status": "success"
}
```

## Architecture

### Core Services

1. **Validator Service** (`/validator`)
   - URL format validation
   - Domain accessibility checks
   - Robots.txt compliance verification
   - SSL certificate validation

2. **Crawler Service** (`/crawler`)
   - Asynchronous web crawling
   - Content extraction and storage
   - Rate limiting and politeness controls

3. **Content Cleaner Service** (`/cleaner`)
   - Raw content processing
   - Noise removal
   - Content structuring

4. **Company Profiler Service** (`/profiler-multi`)
   - Multi-model LLM integration
   - Parallel processing
   - Structured profile generation

5. **Orchestrator Service** (`/orchestrator`)
   - Pipeline coordination
   - Error handling
   - Progress tracking
   - Result aggregation

### LLM Integration

The service supports multiple LLM providers:
- GPT-4 (various models)
- Claude (various models)

### Data Storage

1. **Google Cloud Storage (GCS)**
   - Raw crawled content
   - Cleaned content
   - Generated profiles
   - Metadata
   - Directory structure:
     ```
     enriched_companies/
     └── company-name/
         └── timestamp/
             ├── crawled_pages/
             │   ├── page1.md
             │   ├── page1_meta.json
             │   └── ...
             ├── _RAW.md
             ├── _CLEANED.md
             ├── company_profile.json
             └── metadata.json
     ```

2. **PostgreSQL Database**
   - Company profiles
   - Processing status
   - API keys and configurations

## API Endpoints

### Validator Service
- `POST /validator/validate`: Validate a company URL
- `GET /validator/status`: Check validation status

### Orchestrator Service
- `POST /orchestrator/enrich`: Full pipeline execution
- `GET /orchestrator/status`: Check pipeline status

## Environment Setup

1. **Required Environment Variables**
   ```env
   # Database
   DATABASE_URL=postgresql://user:password@host:port/dbname

   # Google Cloud
   GOOGLE_APPLICATION_CREDENTIALS=path/to/credentials.json
   GCS_OUTPUT_BUCKET_NAME=your_bucket_name

   # API Keys
   OPENAI_API_KEY=your_openai_key
   ANTHROPIC_API_KEY=your_anthropic_key

   # Service Configuration
   MAX_CRAWL_PAGES=50
   CRAWL_DELAY=1.0
   REQUEST_TIMEOUT=30
   ```

2. **Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

## Infrastructure

In order to be able to use `terraform` for `GCP` project we need to take a set of actions listed below:
1. Create project in `GCP` console (UI).
2. Enable billing.
3. Create `Service Account`.
4. Generate key for newly added `Service Account` and store it as `GCP_SERVICEACCOUNT_{DEV|PROD}` CI/CD variable of type `Variable (default)` in current `GitLab` [repo](https://gitlab.com/dealcircle.de/targets-enrichment/-/settings/ci_cd).
5. Add next roles to newly added `Service Account`:
   * `Storage Folder Admin`
   * `Artifact Registry Administrator`
   * `Cloud Run Admin`
   * `Service Account User`
   * `Service Account Admin`
   * `Project IAM Admin`
6. Enable next APIs in `GCP Cloud Console (UI)`:
   * `Artifact Registry API`
   * `Cloud Run Admin API`
   * `Identity and Access Management (IAM) API`
   * `Cloud Resource Manager API`
   * `Secret Manager API`
   * `Cloud SQL Admin API`
7. Add `Cloud Storage` bucket with name of `targets-enrichment-infra-{dev-}terraform-ops` which will be used to store `terraform` state.

## Deployment

### Local Development
1. Create and activate virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   # or
   .\venv\Scripts\activate  # Windows
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your configurations
   ```

4. Run the service:
   ```bash
   uvicorn data_enrichment_api.main_multi:app --reload
   ```

### Production Deployment

1. **Docker Deployment**
   ```bash
   docker build -t dealcircle-enrichment .
   docker run -p 8000:8000 --env-file .env dealcircle-enrichment
   ```

2. **Cloud Run Deployment**
   ```bash
   gcloud builds submit --tag gcr.io/PROJECT_ID/dealcircle-enrichment
   gcloud run deploy dealcircle-enrichment \
     --image gcr.io/PROJECT_ID/dealcircle-enrichment \
     --platform managed \
     --region your-region \
     --allow-unauthenticated
   ```

## Error Handling

The service implements comprehensive error handling:
- Input validation
- Rate limiting
- Timeout management
- Retry mechanisms
- Error logging and monitoring

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

MIT License

## Support

For support, please contact [<EMAIL>] 
