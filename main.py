import asyncio, logging, company_entrichment_service.config

from company_entrichment_service.infrastructure.crawler import Crawler
from data_enrichment_api.services.company_profiler.multimodel_runner import MultiModelRunner

logger = logging.getLogger('__job__')

# website = 'http://grasmehr.de'
website = 'https://www.ford-foerster-koblenz.de/'

def map_result_to_chunks(result):
    seen = set()
    chunks = []

    for r in result:
        unique_lines = []

        for line in r.markdown.splitlines():
            trimmed = line.strip()
            if trimmed and trimmed not in seen:
                seen.add(trimmed)
                unique_lines.append(line)

        lines = '\n'.join(unique_lines).strip()
        chunks.append(f'# Page: {r.url}\n\n{lines}')

    return chunks


crawler = Crawler()
llm_runner = MultiModelRunner()

async def main():
    crawl_result = await crawler.crawl(url=website)
    logger.info(f'crawler result length: {len(crawl_result)}')
    # logger.info(f'crawler result[0]: {crawl_result[0]}')

    chunks = map_result_to_chunks(crawl_result)
    # logger.info(f'chunks: {map_result_to_chunks(crawl_result)}')

    llm_result = await llm_runner.run_all_models_for_chunks_list(chunks)
    logger.info(f'llm result length: {len(llm_result)}')
    logger.info(f'llm result: {llm_result}')

    return

if __name__ == "__main__":
    asyncio.run(main())
